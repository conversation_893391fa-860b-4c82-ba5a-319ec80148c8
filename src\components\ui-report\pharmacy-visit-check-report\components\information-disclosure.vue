<template>
  <div class="information-disclosure-page-v7">
    <template v-for="page in pageContent">
      <div
        class="information-disclosure-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="information-disclosure-t">
          <div class="information-disclosure-tl"></div>
          六、信息披露
        </div>
        <div class="information-disclosure-c">
          <div class="information-disclosure-c-in">
            <div class="p-title">服务公司说明：</div>
            <div class="p-content mgb29">
              本公司以勤勉的职业态度，独立、客观地出具本报告。<br />
              本报告所列数据及信息是本公司合法、公开采集的，但本公司不保证该等信息的准确性或完整性。<br />
              本报告所载的资料、信息、意见及推测只提供给客户作参考之用，并非作为或被视为诊断或唯一性判断。<br />
              分析逻辑基于团队的职业理解，清晰准确地反映了本公司的观点，结论不受任何第三方的授意或影响，特此声明。
            </div>
            <div class="p-title">法律声明：</div>
            <div class="p-content">
              本报告所载内容仅反映于本公司截止发布本报告前的判断。<br />
              在不同时期，本公司可发出与本报告所载数据内容不一致的报告。<br />
              报告及报告中的所有材料的版权归本公司所有，本公司对本报告保留一切权利，未经本公司事先书面授权，本报告的任何部分均不得以任何方式制作任何形式的拷贝、复印件或复制品，或再次分发给任何其他人，或以任何侵犯本公司版权的其他方式使用。所有本报告中使用的商标、服务标记及标记均为本公司的商标、服务标记及标记。
            </div>
          </div>
        </div>

        <pageBottomRectV2
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRectV2>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype", "filePrex"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      projectIco: this.domainUrl + this.filePrex + "icon-project.png",
      timeIco: this.domainUrl + this.filePrex + "icon-timer.png",
      userProjectIco: this.domainUrl + this.filePrex + "icon-project-user.png",
      serviceIco: this.domainUrl + this.filePrex + "icon-service.png",
      subTitle: "",
      pageContent: [
        {
          type: "information-disclosure-page",
          uuid: "information-disclosure-page_0",
          children: [],
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #e7effc;
$mainColorV2: #678ac8;
.information-disclosure-page-v7 {
  background: #fff;
  .information-disclosure-page {
    padding: 72px 20px 50px;
    box-sizing: border-box;
    position: relative;
  }
  .information-disclosure-t {
    font-weight: bold;
    font-size: 32px;
    color: $mainColorV2;
    display: flex;
    align-items: center;
  }
  .information-disclosure-tl {
    margin-right: 25px;
    width: 19px;
    height: 55px;
    background: $mainColorV2;
  }
  .information-disclosure-c {
    padding: 0 20px;
    margin-top: 37px;
  }
  .information-disclosure-c-in {
    background: #e7effc;
    border-radius: 27px;
    padding: 27px 36px;
  }
  .p-title {
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    margin-top: 0;
    margin-bottom: 20px;
    line-height: 20px;
    color: $mainColorV2;
  }
  .p-content {
    font-size: 14px;
    color: #777777;
    line-height: 18px;
  }
  .mgb29 {
    margin-bottom: 20px;
  }
}
</style>