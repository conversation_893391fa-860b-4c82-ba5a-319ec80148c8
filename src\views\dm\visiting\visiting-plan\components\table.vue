<template>
  <div style="height: 100%">
    <el-table
      size="mini"
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      border
      stripe
      class="lvTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />

      <el-table-column
        v-for="(item, index) in titleList"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        :min-width="item.width"
        :show-overflow-tooltip="true"
        :fixed="item.fixed"
      >
        <template slot-scope="scope">
          <div v-if="item.type === 2">
            <!-- pushStatus发布状态 1-草稿 2-已发布 3-已撤销 -->
            <el-button
              type="warning"
              size="mini"
              @click="$emit('showTab', { type: 1, row: scope.row })"
              v-permission="['dm_visiting_plam_edit']"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.pushStatus !== 2"
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 2, row: scope.row })"
              v-permission="['dm_visiting_plam_firing']"
              >启动</el-button
            >
            <el-button
              v-if="scope.row.pushStatus === 2"
              type="danger"
              size="mini"
              @click="$emit('showTab', { type: 3, row: scope.row })"
              v-permission="['dm_visiting_plam_revoke']"
              >撤销</el-button
            >

            <el-button
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 4, row: scope.row })"
              >查看记录</el-button
            >

            <el-button
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 10, row: scope.row })"
              >导出报告</el-button
            >

            <el-button
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 11, row: scope.row })"
              >预览报告</el-button
            >

            <el-button
              type="primary"
              size="mini"
              v-if="scope.row.type === 3"
              @click="$emit('showTab', { type: 12, row: scope.row })"
              >验收报告</el-button
            >
          </div>
          <template v-else-if="item.prop === 'progress'">
            <el-progress
              :percentage="
                scope.row.progress ? +(scope.row.progress * 100).toFixed(2) : 0
              "
            ></el-progress>
          </template>
          <div v-else>{{ scope.row[item.prop] }}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
// import { getWidth } from "@/utils/index";
export default {
  components: {},
  props: {
    tableData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      titleList: [
        { prop: "title", label: "标题", width: "180px" },
        { prop: "physicianInfoName", label: "拜访人", width: "180px" },
        { prop: "time", label: "计划时间", width: "250px" },
        // { prop: 'customerNames', label: '拜访对象', width: '100px' },
        { prop: "typeText", label: "拜访类型", width: "100px" },
        { prop: "progress", label: "拜访进度", width: "180px" },
        { prop: "auditTypeText", label: "审核类型", width: "100px" },

        { prop: "pushStatusText", label: "发布状态", width: "100px" },
        { prop: "projectStatusText", label: "完成状态", width: "100px" },
        { prop: "createTime", label: "创建时间", width: "180px" },
        {
          prop: "",
          label: "操作",
          width: "550px",
          fixed: "right",
          type: 2,
        },
      ],
    };
  },
  watch: {
    tableData(n, o) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      });
    },
  },

  methods: {
    handleSelectionChange(val) {
      this.$emit("select", val);
    },
    showTab(type, row) {
      this.$emit("showTab", { type, row });
    },
  },
};
</script>

<style lang="scss" scoped>
.btnList {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  .btn {
    min-width: 100px;
    margin-right: 10px;
  }
}
</style>
