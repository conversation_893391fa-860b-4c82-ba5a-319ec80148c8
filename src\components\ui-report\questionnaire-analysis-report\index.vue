<template>
  <div
    class="pageContent"
    v-loading="pageLoading"
    :style="{
      '--backgroud-top-bg': 'url(' + domainUrl + bgImg + ')',
      '--backgroud-top-bg-v2': 'url(' + domainUrl + bgImgV2 + ')',
      '--backgroud-top-bg-v3': 'url(' + domainUrl + bgImgV3 + ')',
      '--title-bg': 'url(' + domainUrl + titleBgImg + ')',
      '--border-tb': 'url(' + domainUrl + borderTb + ')',
      '--border-lr': 'url(' + domainUrl + borderLr + ')',
      '--border-tb-v2': 'url(' + domainUrl + borderTbV2 + ')',
      '--border-bb-v2': 'url(' + domainUrl + borderBbV2 + ')',
      '--border-lr-v2': 'url(' + domainUrl + borderLrV2 + ')',
    }"
  >
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <template v-if="page.type === 'thumb'">
          <thumb
            :pageObject="page.pageObject"
            :updatecount="thumbUpdateCount"
            @success="updateSuccess"
            :showPaging="false"
          >
          </thumb>
        </template>
        <template v-if="page.type === 'directory'">
          <directory
            :pageObject="page.pageObject"
            :updatecount="directoryUpdateCount"
            :moduleRangeObject="moduleRangeObject"
            inType="questionnaire-analysis-report"
            @success="updateSuccess"
          ></directory>
        </template>
      </div>
      <template v-else-if="page.authHeight">
        <template v-if="page.type === 'background'">
          <background
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="backgroundUpdateCount"
            :openInner="false"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></background>
        </template>
        <template v-else-if="page.type === 'information'">
          <information
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="informationUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></information>
        </template>
        <template v-else-if="page.type === 'execution-personnel-data'">
          <execution-personnel-data
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="executionPersonnelDataUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="executionPersonnelDataRenderUpdateCount"
          ></execution-personnel-data>
        </template>
        <template
          v-else-if="
            [
              'related-research-modules',
              'dynamics-image-module',
              'some-questionnaire-entries',
              'operator-operation-process',
              'project-execution-process',
            ].includes(page.type)
          "
        >
          <dynamicsImageModule
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="dynamicsImageModuleUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></dynamicsImageModule>
        </template>
        <template
          v-else-if="['research-methods-and-samples'].includes(page.type)"
        >
          <background
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="backgroundUpdateCount"
            :openInner="false"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></background>
        </template>
        <template v-else-if="page.type === 'topic-statistics'">
          <topic-rect-v2
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="topicStatisticsUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="topicStatisticsRenderUpdateCount"
          ></topic-rect-v2>
        </template>
        <template
          v-else-if="
            [
              'conclusions-or-recommendations',
              'service-company-description',
            ].includes(page.type)
          "
        >
          <research-conclusion
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="descUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></research-conclusion>
        </template>
        <template v-else-if="page.type === 'number-of-visits-to-questionnaire'">
          <topic-rect-v4
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="numberOfVisitUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="numberOfVisitRenderUpdateCount"
            :isEchartOpen="false"
          ></topic-rect-v4>
        </template>
      </template>
    </template>
  </div>
</template>

<script>
import thumb from "@/components/ui-report/pharmacy-visit-analysis-report/components/thumb.vue";
import background from "@/components/ui-report/pharmacy-visit-analysis-report/components/background.vue";
import information from "@/components/ui-report/pharmacy-visit-analysis-report/components/information.vue";
import projectExecutionProcess from "@/components/ui-report/pharmacy-visit-analysis-report/components/project-execution-process.vue";
import executionPersonnelData from "@/components/ui-report/pharmacy-visit-analysis-report/components/execution-personnel-data.vue";
import dynamicsForm from "@/components/ui-report/pharmacy-visit-analysis-report/components/dynamics-form.vue";
import dynamicsImageModule from "./components/dynamics-image-module.vue";
import topicRectV2 from "@/components/ui-report/components/topic-rect-v2.vue";
import topicRectV4 from "@/components/ui-report/components/topic-rect-v4.vue";
import researchConclusion from "@/components/ui-report/pharmacy-visit-analysis-report/components/research-conclusion.vue";
import directory from "./components/directory.vue";
import commonMixin from "@/components/ui-report/mixins/common.js";
import { loadScript, domainURL, format } from "@/utils/index";
import { researchAnalysisReport as getVisitingPlanAnalysisReport } from "@/api/research";
import { getQueryStr } from "@/utils/index";
import { commonbusinessdataQueryListData } from "@/api/research";
import { getGender } from "@/utils/enumeration.js";
import { imgServer } from "@/api/config";

export default {
  components: {
    thumb,
    directory,
    background,
    information,
    projectExecutionProcess,
    executionPersonnelData,
    dynamicsForm,
    dynamicsImageModule,
    topicRectV2,
    researchConclusion,
    topicRectV4,
  },
  mixins: [commonMixin],
  provide() {
    return {
      pageSize: this.pageSize,
      domainUrl: this.domainUrl,
      pagetype: Number(this.pagetype),
      pageTopBgUrl: "",
      reportStyle: this.reportStyle,
    };
  },
  props: {
    businessId: {
      type: [Number, String],
      default: null,
    },
    pagetype: {
      type: [Number, String],
      default: "10",
    },
    reportStyle: {
      type: [Number, String],
      default: 1,
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      uuidCountKey: 1000,
      // domainUrl: "http://192.168.3.54:59045/",
      titleBgImg:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-title-bg.png",
      bgImg:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg.png",
      bgImgV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg-v2.png",
      bgImgV3:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg-v3.png",
      borderTb:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-tb.png",
      borderLr:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-lr.png",
      borderTbV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-tb-v3.png",
      borderBbV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-bb-v3.png",
      borderLrV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-lr-v3.png",
      domainUrl: imgServer + "static/",
      domainUrlV2: "https://file.greenboniot.cn/",
      pageLoading: false,
      pageContent: [
        {
          text: "封面",
          type: "thumb",
          moduleKey: "thumb",
          pageObject: {},
        },
        {
          text: "目录",
          type: "directory",
          moduleKey: "directory",
          pageObject: {},
        },
        {
          text: "背景",
          type: "background",
          moduleKey: "background",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行流程",
          type: "project-execution-process",
          moduleKey: "project-execution-process",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目总览",
          type: "information",
          moduleKey: "information",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行人数据统计",
          type: "execution-personnel-data",
          moduleKey: "sex-analysis",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "完成问卷数量分析",
          type: "dynamics-image-module",
          moduleKey: "compute-research-number-analysis",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行人完成问卷数量统计",
          type: "number-of-visits-to-questionnaire",
          moduleKey: "compute-research-number-analysis",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "调研方法与样本",
          type: "research-methods-and-samples",
          moduleKey: "research-methods-and-samples",
          pageObject: {},
          authHeight: true,
        },
        // 相关调研模块
        {
          text: "相关调研模块",
          type: "related-research-modules",
          moduleKey: "related-research-modules",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "表单统计",
          type: "topic-statistics",
          moduleKey: "related-research-modules",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "部分问卷填写内容展示",
          type: "some-questionnaire-entries",
          moduleKey: "related-research-modules",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "执行人操作流程",
          type: "operator-operation-process",
          moduleKey: "operator-operation-process",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "调研结论或建议",
          type: "conclusions-or-recommendations",
          moduleKey: "conclusions-or-recommendations",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "服务公司说明",
          type: "service-company-description",
          pageObject: {},
          moduleKey: "formContent",
          authHeight: true,
        },
      ],
      targetCount: 16,
      fixedFieldObject: {
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己

        productTaskNumber: 0, // 地推团队数量
      },
      thumbUpdateCount: 0,
      directoryUpdateCount: 0,
      backgroundUpdateCount: 0,
      informationUpdateCount: 0,
      projectExecutionProcessUpdateCount: 0,
      executionPersonnelDataUpdateCount: 0,
      executionPersonnelDataRenderUpdateCount: 0,
      dynamicsFormUpdateCount: 0,
      dynamicsImageModuleUpdateCount: 0,
      topicStatisticsUpdateCount: 0,
      topicStatisticsRenderUpdateCount: 0,
      descUpdateCount: 0,
      numberOfVisitUpdateCount: 0,
      numberOfVisitRenderUpdateCount: 0,
    };
  },
  watch: {
    updatecount(n) {
      console.log("updatecount=======", n);
      this.pageLoading = true;
      this.initEchart();
    },
  },
  methods: {
    getDaysInCurrentMonth(taskMonth) {
      let str = taskMonth;
      let arr = str.split("-");
      let year = arr[0];
      let month = arr[1];
      // 设置日期为下个月的第0天，这样它就会回滚到当前月的最后一天
      var nextMonthFirstDay = new Date(year, month, 1);
      var daysInMonth = new Date(nextMonthFirstDay - 1).getDate();
      return daysInMonth;
    },
    pageInitMethod() {
      console.log("pageInitMethod========", this.initsuccesscount);
      return new Promise((resolve, reject) => {
        this.topicStatisticsRenderUpdateCount += 1;
        this.executionPersonnelDataRenderUpdateCount += 1;
        this.numberOfVisitRenderUpdateCount += 1;
        setTimeout(() => {
          resolve(true);
        }, 3000);
      });
    },
    getUuid() {
      this.uuidCountKey += 1;
      return this.uuidCountKey;
    },
    async initEchart() {
      await loadScript(`${this.domainUrlV2}cdnjs/echarts.js`);
      await loadScript(`${this.domainUrlV2}cdnjs/echarts-gl.js`);
      this.initMethod();
    },
    initMethod() {
      const saToken = getQueryStr("satoken");
      if (saToken) {
        this.saTime = 300;
      } else {
        // 客户端预览
        this.saTime = 2000;
      }
      this.exportProjectAccurateId();
    },
    async exportProjectAccurateId() {
      const res = await getVisitingPlanAnalysisReport(
        {
          id: this.businessId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const cRes = await commonbusinessdataQueryListData(
        {
          businessId: this.businessId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const data = res.data;
      const cData = cRes.data;
      let commonObject = {};
      let backgroundObject = {};
      let researchObject = {
        imageResult: [],
      };
      let researchMethodsObject = {
        content: "",
      };
      let researchModulesObject = {
        imageResult: [],
      };
      let someEntriesObject = {
        imageResult: [],
      };
      let processOperationObject = {
        imageResult: [],
      };
      let recommendationsObject = {
        content: "",
      };
      let projectProcessObject = {
        imageResult: [],
      };
      let taskUserVoList = [];
      if (Array.isArray(data.taskUserVoList)) {
        taskUserVoList = data.taskUserVoList;
      }
      let startTime = null;
      let endTime = null;

      if (data.research instanceof Object) {
        commonObject.title = data.research.title;
        commonObject.ctitle = data.research.title;
        commonObject.monthText = data.research.taskMonthly;
        commonObject.serviceProvider = this.fixedFieldObject.projectParty;
        commonObject.projectParty =
          data.research.demanderName || "广东恒源数据服务有限公司";
        backgroundObject.content = data.research.background;
        researchMethodsObject.content = data.research.methodSample;
        recommendationsObject.content = data.research.resultConclusion;
        commonObject.demandLocation = data.research.demandLocation;
        commonObject.demandDescribe = data.research.demandDescribe;
        commonObject.lastMonthDay =
          data.research.taskMonthly !== ""
            ? this.getDaysInCurrentMonth(data.research.taskMonthly)
            : "";
        commonObject.firstMonthDay = "01";
        commonObject.currentMonth =
          data.research.taskMonthly !== ""
            ? data.research.taskMonthly.split("-")[1]
            : "";
        startTime = data.research.startTime;
        endTime = data.research.endTime;

        let time1 = format(startTime, "YYYY-MM-DD");
        let time2 = format(endTime, "YYYY-MM-DD");
        let timeArr1 = time1.split("-");
        let timeArr2 = time2.split("-");
        if (timeArr1[0] !== timeArr2[0]) {
          commonObject.researchStartTime =
            timeArr1[1] + "月" + timeArr1[2] + "日";
          commonObject.researchEndTime =
            timeArr2[1] + "月" + timeArr2[2] + "日";
        } else {
          commonObject.researchStartTime =
            timeArr1[1] + "月" + timeArr1[2] + "日";
          commonObject.researchEndTime =
            timeArr2[1] + "月" + timeArr2[2] + "日";
        }
      }
      if (data.demand instanceof Object) {
        commonObject.taskUserNum = data.demand.taskUserNum;
      }
      let answerReportVoList = [];
      let allWriteOptionNum = 0;
      if (data.answerReportVoList instanceof Object) {
        let cnum = 0;
        answerReportVoList = data.answerReportVoList.map((item) => {
          let type = 1;
          if (item.formType === 1) {
            type = 1;
          } else if (item.formType === 2) {
            type = 2;
          } else {
            type = 3;
          }
          if (allWriteOptionNum < item.allWriteOptionNum) {
            allWriteOptionNum = item.allWriteOptionNum;
          }
          cnum += 1;
          return {
            ...item,
            type,
            title: cnum + '.' + item.formTemplateTitle,
            subTitle: "四、项目执行人数据统计",
            submitCount: item.allWriteOptionNum,
            formTemplateOptions: Array.isArray(item.answerOptionVoList)
              ? item.answerOptionVoList.map((item) => {
                  return {
                    ...item,
                    label: item.optionValue,
                    value: item.optionValue,
                    uuid: this.getUuid(),
                    optionValue: item.optionValue,
                    selectOptionNum: item.selectOptionNum,
                    selectOptionProportion: item.selectOptionProportion + "%",
                  };
                })
              : [],
          };
        });
      }
      // 执行人信息
      let userReportVo = {};
      if (data.userReportVo instanceof Object) {
        userReportVo = data.userReportVo || {};
      }
      commonObject.pharmacyNumber = allWriteOptionNum;
      if (Array.isArray(cData)) {
        cData.forEach((item) => {
          let imageIds =
            item.businessValue !== "" ? item.businessValue.split(",") : [];
          for (let i = 0; i < imageIds.length; i++) {
            let url = domainURL(imageIds[i]);
            if (item.businessType === 2) {
              researchObject.imageResult.push(url);
            } else if (item.businessType === 3) {
              researchModulesObject.imageResult.push(url);
            } else if (item.businessType === 4) {
              someEntriesObject.imageResult.push(url);
            } else if (item.businessType === 5) {
              processOperationObject.imageResult.push(url);
            } else if (item.businessType === 1) {
              projectProcessObject.imageResult.push(url);
            }
          }
        });
      }
      let genderResult = getGender();

      this.pageContent.forEach((item) => {
        switch (item.type) {
          case "thumb":
            item.pageObject = {
              parentUuid: item.type,
              ...commonObject,
            };
            break;
          case "directory":
            item.pageObject = {
              parentUuid: item.type,
              ...commonObject,
            };
            break;
          case "background":
            item.pageObject = {
              parentUuid: item.type,
              ...backgroundObject,
              subTitle: "一、项目背景",
            };
            break;
          case "project-execution-process":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "二、项目执行流程",
              ...projectProcessObject,
            };
            break;
          case "information":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "三、项目总览",
              ...commonObject,
            };
            break;
          case "execution-personnel-data":
            item.pageObject = {
              userReportVo,
              parentUuid: item.type,
              subTitle: "四、项目执行人数据统计",
              // planId: this.businessId,
              // planType: ctype,
            };
            break;
          case "dynamics-image-module":
            item.pageObject = {
              twoTitle: `在 ${commonObject.researchStartTime} - ${commonObject.researchEndTime} 日期间，累计完成 ${allWriteOptionNum} 份问卷`,
              parentUuid: item.type,
              ...researchObject,
              uuidKey: item.type,
            };
            break;
          case "research-methods-and-samples":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "五、调研方法与样本采集",
              ...researchMethodsObject,
              uuidKey: item.type,
            };
            break;
          case "related-research-modules":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "六、相关调研模块",
              twoTitle: "依据项目需求定制专属问卷题目",
              ...researchModulesObject,
              uuidKey: item.type,
            };
            break;
          case "topic-statistics":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVoList,
            };
            break;
          case "some-questionnaire-entries":
            item.pageObject = {
              uuidKey: item.type,
              parentUuid: item.type,
              twoTitle: "部分问卷填写内容展示",
              ...someEntriesObject,
            };
            break;
          case "operator-operation-process":
            item.pageObject = {
              uuidKey: item.type,
              parentUuid: item.type,
              twoTitle: "七、执行人操作流程",
              ...processOperationObject,
            };
            break;
          case "conclusions-or-recommendations":
            item.pageObject = {
              uuidKey: item.type,
              parentUuid: item.type,
              subTitle: "八、调研结论或建议",
              ...recommendationsObject,
            };
            break;
          case "service-company-description":
            item.pageObject = {
              noTitle: true,
              borderOpen: true,
              uuidKey: item.type,
              parentUuid: item.type,
            };
            break;
          case "number-of-visits-to-questionnaire":
            item.pageObject = {
              subTitle: "项目执行人完成问卷数量统计",
              parentUuid: item.type,
              answerReportVo: {
                formTemplateOptions: taskUserVoList.map((item) => {
                  return {
                    optionValue: item.recordName,
                    selectOptionNum: item.submitNum,
                    genderText: this.getEnumText(item.gender, genderResult),
                    userPhone: item.userPhone,
                    submitNum: item.submitNum,
                  };
                }),
              },
            };
            break;
        }
      });
      await this.$nextTick();
      console.log("this.pageContent", this.pageContent);

      this.thumbUpdateCount += 1;
      this.directoryUpdateCount += 1;
      this.backgroundUpdateCount += 1;
      this.informationUpdateCount += 1;
      this.projectExecutionProcessUpdateCount += 1;
      this.dynamicsFormUpdateCount += 1;
      this.executionPersonnelDataUpdateCount += 1;
      this.dynamicsImageModuleUpdateCount += 1;
      this.topicStatisticsUpdateCount += 1;
      this.descUpdateCount += 1;
      this.numberOfVisitUpdateCount += 1;
      this.updateSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
.everyPage {
  overflow: hidden;
  background: #fff;
}
</style>
<style lang="scss">
@import "../css/earth-push-project-report.scss";
</style>
