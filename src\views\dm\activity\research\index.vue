<template>
  <div class="content">
    <searchList :from-data="selectFrom" @tabHeight="getTabHeight">
      <el-col slot="btnList-col" :lg="24" style="margin-bottom: 20px">
        <el-button
          size="mini"
          icon="el-icon-search"
          type="primary"
          class="btn"
          @click="search(false)"
          >查询</el-button
        >
        <el-button
          size="mini"
          type="success"
          @click="add"
          class="btn"
          v-permission="['research_add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="danger"
          @click="stop"
          class="btn"
          v-permission="['research_cease']"
          >停止</el-button
        >

        <!-- <el-button
          size="mini"
          :loading="exportVisitDataReportLoading"
          class="btn"
          type="primary"
          @click="exportVisitDataReport"
          >导出数据报告</el-button
        >
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="previewAnalysisReportCloud(5)"
          >导出数据报告(云导出)</el-button
        > -->
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="exportVisitDataReportXLXS"
          >导出XLSX数据报告</el-button
        >
        <!-- <el-button
          size="mini"
          :loading="previewDataLoading"
          class="btn"
          type="primary"
          @click="previewVisitDataReport"
          >预览数据报告</el-button
        > -->
        <!-- <el-button
          size="mini"
          class="btn"
          type="primary"
          :loading="exportVisitAnalysisReportLoading"
          @click="exportAnalysisReport"
          >导出分析报告</el-button
        > -->
      
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="previewAnalysisReportCloud(3)"
          >导出分析报告(云服务)</el-button
        >

        <!-- <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="previewAnalysisReport"
          >预览分析报告</el-button
        > -->

        <el-button
          size="mini"
          type="primary"
          @click="copyActivity"
          >克隆</el-button
        >

        <el-button
          size="mini"
          type="danger"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
        <el-button
          size="mini"
          type="primary"
          @click="batchLinkProject"
          >批量关联项目</el-button
        >
        <up-streaming-button style="margin-left: 10px" :selectArr='selectArr'></up-streaming-button>
      </el-col>
    </searchList>

    <!-- <div class="tabel" :style="{ height: tabHeight }"> -->
    <div class="tabel">
      <tab
        :table-data="tableData.records"
        :loading="loading"
        @select="select"
        @showTab="showTab"
      />
      <pagination
        :current="current"
        :size="size"
        :total="tableData.total"
        @handleSizeChange="handleSizeChange"
        @handleCurrentChange="handleCurrentChange"
      />
    </div>

    <add :show.sync="addShow" :paramsData="paramsData" />
    <!-- <check :show.sync="checkShow" :paramsData="checkParamsData" /> -->
    <restart :show.sync="restartShow" :paramsData="restartParamsData" />
    <audit-collect
      :show.sync="auditCollectShow"
      :paramsData="auditCollectParamsData"
      :businessConfig="auditBusinessConfig"
    />

    <!-- 多级审核 -->
    <multiAuditCollect
      :show.sync="multiAuditCollectShow"
      :paramsData="multiAuditCollectData"
      :businessConfig="multiAuditCollectCofig"
      :type="multType"
      :updatecount="multiUpdateCount"
    ></multiAuditCollect>

    <previewPrint
      :visible="previewReportVisible"
      :updatecount="previewUpdateCount"
      :taskId="taskId"
      :collectionType="collectionTypeValue"
      @close="previewReportVisible = false"
    ></previewPrint>

    <div class="hidden-box">
      <!-- <div> -->
      <exportPDF
        ref="exportPdfRef"
        @compute="computeExport"
        :filename="'推广任务'"
        :pageSize="pageSize"
        :landscape="true"
      >
        <exportQuestionnaireActivityReport
          @compute="computeSave"
          :updatecount="updatecountExportCount"
          :taskId="taskId"
          :activityType="2"
          :pageSize="pageSize"
          :preview="false"
          :collectionType="collectionTypeValue"
        ></exportQuestionnaireActivityReport>
      </exportPDF>
    </div>

    <el-dialog
      title="导出加载中..."
      :visible.sync="exportLoading"
      width="500px"
      :before-close="handleClose"
    >
      <!-- <span></span> -->
      <div class="exportLoadingBox" v-loading="exportLoading"></div>
    </el-dialog>

    <!-- 数据报告 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef4"
        @compute="computeExport"
        :filename="questionnaireFilename + '数据报告'"
        :exportType="2"
        :pageSize="questionnaireDataUpdatecountExportCountPageSize"
        boxId="questionnaireDataUpdatecountExportReport"
        @updateWidth="updateDataWidth"
      >
        <exportQuestionnaireDataActivityReport
          @compute="computeDataSaveTwo"
          :updatecount="dataUpdatecountExportCount"
          :taskId="questionnaireTaskId"
          :taskIdResult="taskIdResult"
          :filename="questionnaireFilename"
          :boxId="'exportQuestionnaireDataActivityReport2'"
          :pageSize="questionnaireDataUpdatecountExportCountPageSize"
          @updateWidth="updateDataWidth"
        ></exportQuestionnaireDataActivityReport>
      </exportPDF>
    </div>

    <!-- 分析报告 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef5"
        @compute="computeExport"
        :filename="questionnaireFilename + '分析报告'"
        :exportType="2"
        :landscape="false"
        :isA4="true"
        :targetPage="questionnaireAnalysisUpdatecountExportCountPageSize2"
        uuid="exportQuestionnaireAnalysisActivityReport2"
        :pageSize="questionnaireAnalysisUpdatecountExportCountPageSize"
        boxId="exportQuestionnaireAnalysisActivityReport"
        @updateWidth="updateAnalysisWidth"
      >
        <!-- :isNoSpace="true" -->
        <exportQuestionnaireAnalysisActivityReport
          @compute="computeAnalysisSaveTwo"
          :updatecount="visitAnalysisUpdatecountExportCount"
          :taskId="questionnaireTaskId"
          :filename="questionnaireFilename"
          :pageSize="questionnaireAnalysisUpdatecountExportCountPageSize"
        ></exportQuestionnaireAnalysisActivityReport>
        <!-- @updateWidth="updateDataWidth" -->
      </exportPDF>
    </div>

    <previewQuestionnaireData
      :visible="previewQuestionnaireDataVisible"
      :updatecount="previewQuestionnaireUpdateCount"
      :taskId="questionnaireTaskId"
      :taskIdResult="taskIdResult"
      @close="previewQuestionnaireDataVisible = false"
      :filename="questionnaireFilename"
    ></previewQuestionnaireData>

    <previewQuestionnaireAnalysisActivityReport
      :visible="previewQuestionnaireAnalysisVisible"
      :updatecount="previewQuestionnaireAnalysisUpdateCount"
      :taskId="questionnaireTaskId"
      @close="previewQuestionnaireAnalysisVisible = false"
      :filename="questionnaireFilename"
    ></previewQuestionnaireAnalysisActivityReport>

    <a :href="aHref" ref="aRef" target="_blank"></a>

    <exportXlsx
      :filename="exportVisitDataTitle + '.xlsx'"
      :data="exportVisitDataXlxs"
      :width="'1280px'"
      :hearders="exportVisitDataHeaders"
      :visible="exportVisitDataVisible"
      @close="exportVisitDataVisible = false"
      @query="exportVisitDataVisible = false"
    ></exportXlsx>
    <!-- 预览报告 -->
    <!-- <exportQuestionnaireActivityReport></exportQuestionnaireActivityReport> -->

    <!-- <retailPharmacyVisitDataReport></retailPharmacyVisitDataReport> -->
    <reportLog :exportName='exportName' :show="showReportLog" :pagetype="pagetype" :selectIds='selectIds' @close='closeReport' :landscape='landscape'></reportLog>
    <el-image-viewer
      v-if="dialogVisible"
      :on-close="() => {dialogVisible = false}"
      :url-list="imgArr.map(item => item.url)"
      :initialIndex="imgIndex"
    ></el-image-viewer>

    <copy-activity
      :show.sync="copyActivityShow"
      :paramsData="copyActivityParamsData"
      @close="copyActivityClose"
      :query-params="{ activityType: 2 }"
    />
    <!-- 批量关联项目 -->
    <batchProject :visible='batchProjectVisible' :updatecount='batchUpdateCount' :deviceInfos='selectArr' @close='closeBatchProject'></batchProject>
  </div>
</template>

<script>
import { getFromData, parseTime } from "@/utils/index";
import { getRunStatus, getDmcollectionTypeResult,getDmExamineAuditTypeResult } from "@/utils/enumeration";

import tab from "./components/table.vue";
import pagination from "@/components/MyPagination";
import add from "./components/add/index";
// import check from './components/check'
import restart from "./components/restart";
import auditCollect from "@/views/dm/activity/caseCollect/components/audit-collect/index.vue";

import multiAuditCollect from "@/views/dm/activity/caseCollect/components/audit-collect/multi-audit.vue";

import {
  queryPage,
  queryList,
  deleteBatch,
  updateStatus,
  researchGetQrCode,
} from "@/api/research";

import previewPrint from "./components/previewPrint.vue";

import exportQuestionnaireActivityReport from "@/components/exportPDF/template/export-questionnaire-activity-report.vue";

import exportPDF from "@/components/exportPDF/index";

import exportQuestionnaireDataActivityReport from "@/components/exportPDF/template/export-questionnaire-data-activity-report.vue";
import exportQuestionnaireAnalysisActivityReport from "@/components/exportPDF/template/export-questionnaire-analysis-activity-report.vue";

import previewQuestionnaireData from "./components/previewQuestionnaireData/index.vue";

import previewQuestionnaireAnalysisActivityReport from "./components/previewQuestionnaireAnalysisActivityReport/index.vue";

import exportXlsx from "@/components/exportXlsx/index.vue";

import { researchGetReport, researchExportExcel } from "@/api/research.js";

// import retailPharmacyVisitDataReport from "@/components/exportPDF/template/retail-pharmacy-visit-data-report.vue";

import reportLog from '@/views/dm/components/report-log/index.vue'
import { domainURL } from '@/utils/index';
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
import copyActivity from '@/views/dm/activity/caseCollect/components/copy-activity.vue'
import batchProject from './components/batch-project/index';
import {
  queryList as getDemandIdList,
} from "@/api/dmDemand";
import upStreamingButton from './components/up-streaming-button/index.vue';
export default {
  name: "demand",
  components: {
    tab,
    pagination,
    add,
    // check,
    restart,
    auditCollect,
    multiAuditCollect,
    previewPrint,
    exportQuestionnaireActivityReport,
    exportPDF,
    exportQuestionnaireDataActivityReport,
    exportQuestionnaireAnalysisActivityReport,
    previewQuestionnaireData,
    previewQuestionnaireAnalysisActivityReport,
    exportXlsx,
    reportLog,
    ElImageViewer,
    batchProject,
    copyActivity,
    upStreamingButton
  },
  provide() {
    return {
      statusObj: this.statusObj,
    };
  },
  data() {
    return {
      // 批量关联项目
      batchProjectVisible:false,
      batchUpdateCount:0,

      landscape:true,
      exportName:"",
      dialogVisible:false,
      imgArr:[],
      imgIndex:0,

      // 导出分析报告云服务
      selectIds:[],
      showReportLog:false,
      pagetype:3,// 3 是问卷分析报告 5 问卷数据报告



      exportVisitDataTitle:"",
      exportVisitDataVisible: false,
      exportVisitDataHeaders: [],
      exportVisitDataXlxs: [],

      aHref: null,
      // 导出分析报告
      questionnaireAnalysisUpdatecountExportCountPageSize: {
        // height: 595,
        height: 595,
        width: 881,
      },
      questionnaireAnalysisUpdatecountExportCountPageSize2: {
        width: 595,
        height: 881,
      },
      visitAnalysisUpdatecountExportCount: 0,
      // 预览分析报告
      previewQuestionnaireAnalysisVisible: false,
      previewQuestionnaireAnalysisUpdateCount: 0,

      // 预览数据报告
      previewQuestionnaireDataVisible: false,
      previewQuestionnaireUpdateCount: 0,

      taskIdResult: [],
      // 问卷
      dataUpdatecountExportCount: 0,
      questionnaireTaskId: null,
      questionnaireFilename: null,
      questionnaireDataUpdatecountExportCountPageSize: {
        height: 595,
        width: 881,
      },

      exportVisitDataReportLoading: false,
      previewDataLoading: false,
      exportVisitAnalysisReportLoading: false,

      collectionTypeValue: 1,
      pageSize: {
        height: 595,
        width: 881,
      },
      // exportLoading: false,
      updatecountExportCount: 0,

      previewReportVisible: false,
      previewUpdateCount: 0,
      taskId: null,

      exportLoading: false,
      previewReportVisible: false,
      previewUpdateCount: 0,
      taskId: null,

      multiUpdateCount: 0,
      multType: 1, // 1 征集 2 回访
      multiAuditCollectShow: false,
      multiAuditCollectData: null,
      multiAuditCollectCofig: {
        type: 1, // 流程类型：1-征集，2-回访
        activityType: 2, // 1-病例征集 2-问卷活动
      },

      tabHeight: 0,
      tableData: {},
      current: 1,
      size: 10,
      loading: false,
      selectArr: [],
      selectFrom: [
        {
          title: "调研名称",
          id: "researchId",
          value: null,
          type: 2,
          option: [],
        },
        {
          title: "类型",
          id: "collectionType",
          value: null,
          type: 2,
          option: getDmcollectionTypeResult(),
        },
        {
          title: "运营状态",
          id: "runStatus",
          value: null,
          type: 2,
          option: getRunStatus(),
        },
        {
          title: "审核类型",
          id: "auditType",
          value: null,
          type: 2,
          option: getDmExamineAuditTypeResult(),
        },
        {
          title: "关联项目",
          id: "demandId",
          value: null,
          type: 2,
          option:[],
        },
        // runStatus: 运行状态：1-草稿，2-进行中，3-已结束
        // collectionType: 类型, 1个人, 2患者推广, 3精准地推, 4企业推广
        // auditType: 审核类型:1-默认，2-多级

      ],
      statusObj: {
        addDisabled: false,
      },
      paramsData: null,
      addShow: false,
      // checkShow: false,
      // checkParamsData: null,
      restartShow: false,
      restartParamsData: null,
      auditCollectParamsData: null,
      auditCollectShow: false,
      auditBusinessConfig: {
        type: 1, // 流程类型：1-征集，2-回访
        activityType: 2, // 1-病例征集 2-问卷活动
      },
      copyActivityShow: false,
      copyActivityParamsData: null
    };
  },
  created() {
    this.queryList();
    this.search();
    this.getLinkProject()
    // setTimeout(() => {
    //   this.dataUpdatecountExportCount += 1
    // },2000)
  },
  watch: {
    addShow() {
      if (!this.addShow) {
        this.paramsData = null;
        this.queryList();
        this.search(true);
      }
    },
    // checkShow () {
    //   if(!this.checkShow) {
    //     this.checkParamsData = null
    //     this.search(true)
    //   }
    // },
    restartShow() {
      if (!this.restartShow) {
        this.restartParamsData = null;
        this.search(true);
      }
    },
    auditCollectShow() {
      if (!this.auditCollectShow) {
        this.auditCollectParamsData = null;
        this.search(true);
      }
    },
  },
  methods: {
    // 获取关联项目
    async getLinkProject() {
      const res = await getDemandIdList();
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.title,
        };
      });
      this.setFormData("demandId", "option", data);
    },
    closeBatchProject(type) {
      if(type === 'query') {
        this.search()
      }
      this.batchProjectVisible = false;
    },
    // 批量关联项目
    batchLinkProject() {
      if(this.selectArr.length === 0) {
        return this.$eltool.errorMsg('请选择需要批量关联的项目的条目');
      }

      this.batchProjectVisible = true;
      this.$nextTick(() => {
        this.batchUpdateCount += 1;
      })
    },
    //批量删除
    handleBatchDelete(){
      if (this.$validate.isNull(this.selectArr)) {
        return this.$eltool.warnMsg('至少选中一个')
      }
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let ids = this.selectArr.map(item=>item.id)
        console.log(ids,'ids')
        const res = await deleteBatch({ids:ids.join(',')})
        this.$eltool.successMsg(res.msg);
        this.search(true);
      })
    },

    copyActivityClose({ type }) {
      if (type === 'query') {
        this.search(true)
      }
      this.copyActivityParamsData = null
    },
    copyActivity() {
      if (this.$validate.isNull(this.selectArr)) {
        return this.$eltool.warnMsg('请选中一个活动进行克隆')
      }
      if (this.selectArr.length !== 1) {
        return this.$eltool.warnMsg('只能选中一个活动进行克隆')
      }
      this.copyActivityShow = true
      this.copyActivityParamsData = this.selectArr[0]
    },
    closeReport(){
      this.showReportLog = false
    },
    // 导出分析计划（云服务）
    previewAnalysisReportCloud(pagetype){
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("必须选中一条");
      } else if(this.selectArr.length > 1){
        return this.$eltool.errorMsg("只能选中一条");
      }
      this.selectIds = this.selectArr.map(item => {
        return {
          businessId:item.id,
        }
      });
      this.pagetype = pagetype;
      this.exportName = this.selectArr[0].title;
      this.landscape = true;
      if(pagetype === 3){
        this.exportName += '分析报告'
      }else {
        this.exportName += '数据报告'
      }

      this.$nextTick(() => {
        this.showReportLog = true;
      })
    },
    async exportVisitDataReportXLXS() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("请选择要操作条目");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能操作一条数据");
      }
      this.$requestV1.download(`${researchExportExcel}?id=${this.selectArr[0].id}`, {}, `${this.selectArr[0].title}.xlsx`, 'post')
    },
    updateAnalysisWidth(width) {},
    updateDataWidth(width) {
      this.questionnaireDataUpdatecountExportCountPageSize.width = width;
    },
    computeDataSaveTwo() {
      // 导出报告
      this.$refs.exportPdfRef4.loadPdf();
    },
    computeAnalysisSaveTwo() {
      // 导出报告
      this.$refs.exportPdfRef5.loadPdf();
    },

    previewAnalysisReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("同一时间只能操作一个任务");
      }
      let row = this.selectArr[0]

      // 预览报告
      let location = window.location;
      let params = {
        businessId:row.id,
        pagetype: this.pagetype,
      };
      let str = "";
      for (let key in params) {
        if (str === "") {
          str += key + "=" + params[key];
        } else {
          str += "&" + key + "=" + params[key];
        }
      }

      this.aHref =
        location.origin +
        location.pathname +
        "#" +
        `/servePrint?${str}`;
      this.$nextTick(() => {
        this.$refs.aRef.click();
      });
    },
    exportAnalysisReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("同一时间只能操作一个任务");
      }
      this.questionnaireTaskId = this.selectArr[0].id;
      this.questionnaireFilename = this.selectArr[0].title;
      this.exportLoading = true;

      this.$nextTick(() => {
        this.visitAnalysisUpdatecountExportCount += 1;
      });
    },
    previewVisitDataReport() {
      if (this.selectArr.length === 0) {
        this.$eltool.errorMsg("至少选中一个任务");
        return;
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("同一时间只能操作一个任务");
      }
      let ids = [];
      for (let i = 0; i < this.selectArr.length; i++) {
        ids.push(this.selectArr[i].id);
      }
      this.taskIdResult = ids;
      this.previewQuestionnaireDataVisible = true;
      // this.filename = ''
      this.questionnaireFilename = "问卷";

      this.$nextTick(() => {
        this.previewQuestionnaireUpdateCount += 1;
      });
    },
    exportVisitDataReport() {
      if (this.selectArr.length === 0) {
        this.$eltool.errorMsg("至少选中一个任务");
        return;
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("同一时间只能操作一个任务");
      }

      let row = this.selectArr[0];

      // 预览报告
      let location = window.location;
      let params = {
        taskId: row.id,
        // userId: row.userId,
        // pageId: row.id,
      };
      let str = "";
      for (let key in params) {
        if (str === "") {
          str += key + "=" + params[key];
        } else {
          str += "&" + key + "=" + params[key];
        }
      }

      this.aHref =
        location.origin + location.pathname + "#" + `/dm/preview/index?${str}`;

      this.$nextTick(() => {
        this.$refs.aRef.click();
      });
      // let ids = [];
      // for (let i = 0; i < this.selectArr.length; i++) {
      //   ids.push(this.selectArr[i].id);
      // }
      // this.taskIdResult = ids;
      // this.exportLoading = true;
      // this.questionnaireFilename = "问卷";

      // this.$nextTick(() => {
      //   this.dataUpdatecountExportCount += 1;
      // });
    },

    handleClose() {
      this.exportLoading = false;
    },
    computeSave() {
      // 导出报告
      this.$refs.exportPdfRef.loadPdf();
    },
    updateWidth(width) {
      this.questionnaireDataUpdatecountExportCountPageSize.width = width;
    },
    computeExport() {
      this.exportLoading = false;
      this.$eltool.successMsg("导出成功，详情请看下载记录");
    },
    stop() {
      if (this.$validate.isNull(this.selectArr)) {
        this.$eltool.warnMsg("至少选择一个问卷活动！");
        return;
      }
      this.$confirm("是否确认停止?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {});
    },
    async queryList() {
      const res = await queryList();
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.title,
        };
      });
      this.setFormData("researchId", "option", data);
    },
    add() {
      this.paramsData = null;
      this.addShow = true;
      this.statusObj.addDisabled = false;
    },
    showTab({ type, row }) {
      console.log(row);
      switch (type) {
        case 1:
          // 查看
          this.paramsData = row;
          this.addShow = true;
          this.statusObj.addDisabled = true;
          break;
        case 2:
          // 编辑
          this.paramsData = row;
          this.addShow = true;
          this.statusObj.addDisabled = false;
          break;
        case 3:
          // 开始
          this.$confirm("是否确认开始?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(() => {
            updateStatus({
              runStatus: 2,
              id: row.id,
              startTime: parseTime(new Date()),
            }).then((res) => {
              this.$eltool.successMsg(res.msg);
              this.search();
            });
          });
          break;
        case 4:
          // 重回收
          this.restartParamsData = row;
          this.restartShow = true;
          break;
        case 5:
          // 停止
          this.$confirm("是否确认停止?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            updateStatus({
              runStatus: 3,
              id: row.id,
              endTime: parseTime(new Date()),
            }).then((res) => {
              this.$eltool.successMsg(res.msg);
              this.search();
            });
          });
          break;
        // case 6:
        //   // 删除
        //   this.$confirm('是否确认删除?', '提示', {
        //       confirmButtonText: '确定',
        //       cancelButtonText: '取消',
        //       type: 'warning'
        //   }).then( async () => {
        //       const res = await deleteBatch({ids: row.id})
        //       this.$eltool.successMsg(res.msg)
        //       this.search()
        //       this.queryList()
        //   })
        //   break
        case 7:
          // 审核问卷
          // this.auditCollectParamsData = row;
          // this.auditCollectShow = true;
          // row.auditType = 2;
          if (row.auditType === 2) {
            this.multiAuditCollectShow = true;
            this.multiAuditCollectData = row;
            this.multiAuditCollectCofig = {
              type: 1, // 流程类型：1-征集，2-回访
              activityType: 2, // 1-病例征集 2-问卷活动
              collectionType: row.collectionType,
            };

            this.multType = 1;

            this.$nextTick(() => {
              this.multiUpdateCount += 1;
            });
          } else {
            // 审核回访
            this.auditCollectParamsData = row;
            this.auditCollectShow = true;
            this.auditBusinessConfig = {
              type: 1, // 流程类型：1-征集，2-回访
              activityType: 2, // 1-病例征集 2-问卷活动
              collectionType: row.collectionType,
            };
          }
          break;

        case 11:
          // 预览报告
          this.previewReportVisible = true;
          this.taskId = row.id;
          this.collectionTypeValue = row.collectionType;

          this.$nextTick(() => {
            this.previewUpdateCount += 1;
          });

          break;

        case 10:
          // 导出报告
          // this.taskId = row.id;
          // this.exportLoading = true;
          // this.collectionTypeValue = row.collectionType;

          // this.$nextTick(() => {
          //   this.updatecountExportCount += 1;
          // });
          this.selectIds = [
            {
              businessId:row.id,
            }
          ]
          this.pagetype = 7;
          this.exportName = row.title + '数据报告';
          this.landscape = true;

          this.$nextTick(() => {
            this.showReportLog = true;
          })
          break;
        case 12:
          // 二维码
          this.$confirm("是否执行生成推广码操作?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
             row.recommendLoading = true;
             researchGetQrCode({
              researchId:row.id,
              path:'modules/activity/questionnaire/index',
             }).then(res => {
               row.recommendLoading = false;
                this.imgArr = [
                  {
                    url:res.data,
                  }
                ]
                this.$nextTick(() => {
                  this.dialogVisible = true;
                })
                // this.$eltool.successMsg(res.msg);
                // this.search()
             }).catch(e => {
              row.recommendLoading = false;
             })
          });
          break;
        case 13:
          this.pagetype = 23;
          // 验收报告
          this.selectIds = [
            {
              taskId:row.id,// 任务id
            }
          ]
          this.landscape = false;
          this.$nextTick(() => {
            this.showReportLog = true;
          })

          break;
        // default:
      }
    },
    select(val) {
      this.selectArr = val;
    },
    setFormData(id, key, value) {
      this.selectFrom.forEach((item, index) => {
        if (item.id === id) {
          this.selectFrom[index][key] = value;
        }
      });
    },
    handleSizeChange(val) {
      this.size = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.current = val;
      this.search(true);
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    getTabHeight(height) {
      this.tabHeight = height;
    },
    search(noreset) {
      if (!noreset) {
        this.size = 10;
        this.current = 1;
      }
      this.loading = true;
      const size = this.size;
      const current = this.current;

      let condition = getFromData(this.selectFrom);
      let collectionTypes = [1,2,3,4,6] //默认这些类型 过滤健康自测类型
      condition.collectionTypes = collectionTypes.join(',') 
      const getDmcollectionTypeResultArr = getDmcollectionTypeResult();
      queryPage({ size, current, condition })
        .then((res) => {
          res.data.records = res.data.records.map((item) => {
            return {
              ...item,
              qrcodePathText: item.qrcodePath !== "" ? domainURL(item.qrcodePath) : "",
              runStatusText: item.collectionType == 5 ? '--' : this.getEnumText(item.runStatus, getRunStatus()),
              openStatusText: item.collectionType == 5 ? '--' : this.getEnumText(item.openStatus, [
                { label: "是", value: 1 },
                { label: "否", value: 2 },
              ]),
              scopeTime: `${item.startTime}-${item.endTime}`,
              auditTypeText: item.auditType === 2 ? "多级审核" : item.collectionType == 5 ? '--' : "默认审核",
              collectionTypeText: this.getEnumText(
                item.collectionType,
                getDmcollectionTypeResultArr
              ),
              recommendLoading:false,
            };
          });
          this.tableData = res.data;
        })
        .then((res) => {
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.btn{
  margin-bottom: 10px;
}
.exportLoadingBox {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}
.hidden-box {
  height: 0;
  overflow: hidden;
}
::v-deep .content .search {
  min-height: 0px;
  .main {
    display: flex;
    width: 350px;
    margin-right: 10px;
    .span {
      min-width: 125px;
      line-height: 40px;
    }
  }
}
</style>
