<template>
  <el-dialog
    :title="subtitle + '报告任务月度选择'"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    center
    append-to-body
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '100px' }"
    >
      <div slot="btnList">
        <el-button type="primary" size="mini" @click="confirm" :loading='confirmLoading'>
          确定
        </el-button>
        <el-button type="danger" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
    <reportLog
      :show="showReportLog"
      :pagetype="pagetype"
      :selectIds="selectIds"
      @close="closeReport"
      :landscape="false"
      :exportName="exportName"
      :noBusinessId='true'
    ></reportLog>

    <configureReportData
      :visible="configureReportDataShow"
      :paramsData="configureReportParamsData"
      :updatecount="configureReportDataUpdateCount"
      @close="closeConfigureReportData"
    ></configureReportData>
  </el-dialog>
</template>

<script>
import {
  getProductReportTypeList,
  getPayChannel,
  getTaskType,
} from "@/utils/enumeration";
import { getFromData } from "@/utils/index";
import reportLog from "@/views/dm/components/report-log/index.vue";
import { format } from "@/utils/index";
import formMixin from "@/mixin/formMixin";
import { queryList as useworkconfigQueryList } from "@/api/dm/base-config/useworkconfig";
import configureReportData from "./configure-report-data/index.vue";
import { todotasksExportReportData, todotasksExportReportDataOrdinary, todotasksExportReportDataUser, todotasksExportReportDataNodes } from "@/api/dmDemand";
export default {
  mixins: [formMixin],
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    // 1 导出项目服务报告 2 配置报告数据
    dialogType: {
      type: Number,
      default: 1,
    },
  },
  components: {
    reportLog,
    configureReportData,
  },
  data() {
    const getProductReportTypeListArr = getProductReportTypeList();
    return {
      confirmLoading: false,
      // 配置项目报告数据
      configureReportDataUpdateCount: 0,
      configureReportDataShow: false,
      configureReportParamsData: null,
      dialogVisible: false,
      formList: [
        {
          title: "报告类型",
          type: 2,
          id: "pagetype",
          value: getProductReportTypeListArr[0].value,
          option: getProductReportTypeListArr,
          clearable: false,
        },
        {
          title: "任务月度",
          type: 5,
          id: "taskMonthly",
          value: null,
          dateType: "month",
          format: "yyyy-MM",
          must: true,
        },
        {
          title: "任务月度",
          type: 5,
          id: "taskMonthlyRange",
          value: null,
          dateType: "datetimerange",
          format: 'yyyy-MM-dd HH:mm:ss',
          defaultTime: ['00:00:00','23:59:59'],
          hidden: true,
          multiable: true,
          disable:false,
          must: true,
        },
        {
          title: "支付渠道",
          type: 2,
          id: "paymentChannels",
          value: null,
          option: [],
          hidden: true,
          must: true,
        },
        {
          title: "任务类型",
          type: 2,
          id: "taskTypes",
          value: null,
          option: getTaskType().filter(
            (item) => ![9, 10, 11].includes(item.value)
          ),
          hidden: true,
          multiple: true,
          // must: true,
        },
      ],
      exportName: "",
      pagetype: 10,
      showReportLog: false,
      selectIds: [],
      getProductReportTypeListArr,
    };
  },
  watch: {
    visible(n) {
      this.dialogVisible = n;
      if (!n) {
        this.clear();
      } else {
        console.log('this.dialogType',this.dialogType)
        if([2,3].includes(this.dialogType)) {
          let varr = [14];
          if(this.dialogType === 2) {
            varr = [14,15,16,17]
          }
          let options = getProductReportTypeList().filter(item =>  !varr.includes(item.value));
          this.setFormData('pagetype','option',options)
        } else {
          let options = getProductReportTypeList()
          this.setFormData('pagetype','option',options)
        }
        this.useworkconfigQueryList();
      }
    },
    cpagetype(n) {
      if (n === 11) {
        this.setFormData("paymentChannels", "hidden", false);
        this.setFormData("taskTypes", "hidden", false);
      } else {
        this.setFormData("paymentChannels", "hidden", true);
        this.setFormData("taskTypes", "hidden", true);
      }
      this.setFormData('taskMonthlyRange','hidden',n !== 14);
      this.setFormData('taskMonthly','hidden',n === 14);
    },
  },
  methods: {
    closeConfigureReportData() {
      this.configureReportDataShow = false;
    },
    async useworkconfigQueryList() {
      const res = await useworkconfigQueryList({ openStatus: 1 });
      const payChannelList = getPayChannel();
      let list = res.data.map((item) => {
        const curItem = payChannelList.find((e) => item.payChannel === e.value);
        let label = "";
        if (curItem) {
          const commissionRateText = `（税费：${
            !this.$validate.isNull(item.commissionRate)
              ? (+this.$df.divide(+item.commissionRate, 100)).toFixed(2)
              : 0
          }%）`;
          if (item.payChannel === 1) {
            label = curItem.label + commissionRateText;
          } else {
            label = curItem.label + "-" + item.companyName + commissionRateText;
          }
        }
        return {
          ...item,
          value: item.id,
          label,
        };
      });
      this.setFormData("paymentChannels", "option", list);
    },
    closeReport() {
      this.showReportLog = false;
    },
    clear() {
      this.formList.forEach((item, index) => {
        if (item.id === "pagetype") {
          this.formList[index].value =
            this.getProductReportTypeListArr[0].value;
        } else {
          this.formList[index].value = null;
        }
      });
    },
    async confirm() {
      let formParam = getFromData(this.formList);
      if (!formParam) return;
      let uname = format(formParam.taskMonthly, "YYYY-MM");
      let businessId = format(formParam.taskMonthly, "YYYY-MM")

      if (formParam.pagetype === 10) {
        this.exportName =
          uname +
          "-关于小葫芦平台在全国区域的精准地推活动项目报告";
      } else if (formParam.pagetype === 11) {
        this.exportName =
          uname +
          "-关于小葫芦平台在全国区域的普通地推活动项目报告";
      } else if (formParam.pagetype === 13) {
        this.exportName = uname + "-关于小葫芦平台的线上推广项目";
      } else if (formParam.pagetype === 14) {
        businessId = new Date(formParam.taskMonthlyRange[0]).getTime() + 'to' + new Date(formParam.taskMonthlyRange[1]).getTime()
        uname = format(formParam.taskMonthlyRange[0], 'YYYY-MM-DD HH:mm:ss') + '至' + format(formParam.taskMonthlyRange[1], 'YYYY-MM-DD HH:mm:ss')
        this.exportName = uname + "-关于陪诊平台的技术开发服务外包项目";
      } else if (formParam.pagetype === 15) {
        this.exportName = uname + '-关于小葫芦平台的线上用户活动项目'
      } else if (formParam.pagetype === 16) {
        this.exportName = uname + '-关于小葫芦平台的线下用户活动项目'
      } else if (formParam.pagetype === 17) {
        this.exportName = uname + '-关于科普笔记项目报告'
      }
      this.pagetype = formParam.pagetype;
      if(this.pagetype === 14) {
        this.pagetype = 15;
      } else if(this.pagetype === 15) {
        this.pagetype = 18; // 线上用户活动项目报告
      } else if(this.pagetype === 16) {
        this.pagetype = 19; // 线下用户活动项目报告
      } else if(this.pagetype === 17) {
        this.pagetype = 21; // 科普笔记项目报告
      }
      let taskTypes = formParam.taskTypes;
      if (Array.isArray(taskTypes)) {
        taskTypes = taskTypes.join("-");
      } else {
        taskTypes = "";
      }
      let str = formParam.paymentChannels + "-Im-" + taskTypes;
      let additional = encodeURIComponent(str);
      console.log("additional", additional);
      this.selectIds = [
        {
          businessId,
          businessType: formParam.pagetype,
          additional: additional,
        },
      ];
      console.log("this.selectIds", this.selectIds);

      if (this.dialogType === 2) {
        this.configureReportDataShow = true;
        this.configureReportParamsData = {
          taskMonth: format(formParam.taskMonthly, "YYYY-MM"),
          businessType: formParam.pagetype,
          additional: additional,
          exportName: this.exportName,
        };
        this.$nextTick(() => {
          this.configureReportDataUpdateCount += 1;
        });
      } else if (this.dialogType === 3) {
        let formType = null;
        if (formParam.pagetype === 10) {
          formType = 3;
        } else if (formParam.pagetype === 13) {
          formType = 6;
        } else if (formParam.pagetype === 15) {
          formType = 30
        } else if (formParam.pagetype === 16) {
          formType = 32
        }
        this.confirmLoading = true;
        if(formParam.pagetype === 11) {
          // 普通地推类型
          let res = await todotasksExportReportDataOrdinary({
            taskMonth: format(formParam.taskMonthly, "YYYY-MM"),
            useWorkConfigId: formParam.paymentChannels,
            taskTypes: formParam.taskTypes.join(','),
            // type: formType,
            // fileName: 
          },
          this.exportName + '_数据统计.xlsx'
          ).catch(e => {
            this.confirmLoading = false;
          });
          this.$eltool.successMsg('下载成功，具体看浏览器右上角')
        } else if([15,16].includes(formParam.pagetype)) {
          // 用户活动项目报告
          let res = await todotasksExportReportDataUser({
            taskMonth: format(formParam.taskMonthly, "YYYY-MM"),
            taskTypes: formType,
          },
          this.exportName + '_数据统计.xlsx'
          ).catch(e => {
            this.confirmLoading = false;
          });
          this.$eltool.successMsg('下载成功，具体看浏览器右上角')
        } else if([17].includes(formParam.pagetype)) {
          let res = await todotasksExportReportDataNodes({
            taskTypes: 31,
            taskMonth: format(formParam.taskMonthly, "YYYY-MM"),
          },this.exportName + '_数据统计.xlsx').catch(e => {
            this.confirmLoading = false;
          });
          this.$eltool.successMsg('下载成功，具体看浏览器右上角')
        } 
        else {
          let res = await todotasksExportReportData({
            taskMonth: format(formParam.taskMonthly, "YYYY-MM"),
            type: formType,
            // fileName: 
          },
          this.exportName + '_数据统计.xlsx'
          ).catch(e => {
            this.confirmLoading = false;
          });
          this.$eltool.successMsg('下载成功，具体看浏览器右上角')
        }
        this.confirmLoading = false;
        this.close('query')

      } else {
        this.$nextTick(() => {
          this.showReportLog = true;
        });
      }
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit("close", type);
    },
  },
  computed: {
    subtitle(str) {
      if(this.dialogType === 1) {
        str = '【导出项目服务报告】'
      } else if(this.dialogType === 2) {
        str = '【配置报告数据】'
      } else if(this.dialogType === 3) {
        str = '【导出项目报告数据】'
      }
      return str
    },
    cpagetype() {
      return this.getFormData("pagetype", "value");
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
