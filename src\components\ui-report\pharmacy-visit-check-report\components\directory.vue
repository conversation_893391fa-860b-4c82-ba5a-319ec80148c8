<template>
  <div class="directory-page-v8">
    <div class="directory-t">目录Contents</div>
    <div
      class="directory-c"
      :style="{
        '--background-url': 'url(' + bgImg + ')',
      }"
    >
      <template v-for="item in directoryResult">
        <div class="directory-i" :key="item.label">
          <div class="directory-i-t">
            <div class="directory-i-tl">
              {{ item.indent }}、{{ item.label }}
            </div>
            <div class="directory-i-tr">
              {{ item.moduleKey ? moduleRangeObject[item.moduleKey] : "" }}
            </div>
          </div>
          <div class="directory-i-c">
            <template v-for="item2 in item.children">
              <div class="directory-i-ci" :key="item2.label">
                <div class="directory-i-ci-l">
                  {{ item2.label }}
                </div>
                <div class="directory-i-ci-r">
                  {{
                    item2.moduleKey ? moduleRangeObject[item2.moduleKey] : ""
                  }}
                </div>
              </div>
            </template>
          </div>
        </div>
      </template>
    </div>
    <div class="directory-bottom"></div>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    moduleRangeObject: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      bgImg:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-check-report/styleOne/icon-directory-bg.png",
      directoryResult: [
        {
          label: "项目基础信息",
          indent: "一",
          moduleKey: "information",
          children: [],
        },
        {
          label: "项目背景介绍",
          indent: "二",
          moduleKey: "information",
          children: [],
        },
        {
          label: "项目执行与验收流程",
          indent: "三",
          children: [
            {
              label: "项目执行概述",
              cnum: 1,
              moduleKey: "project-execution-process",
            },
            {
              label: "验收流程",
              cnum: 2,
              moduleKey: "project-execution-process",
            },
          ],
        },
        {
          label: "项目验收结果",
          indent: "四",
          children: [
            {
              label: "整体验收指标",
              cnum: 1,
              moduleKey: "project-execution-result",
            },
            {
              label: "执行人任务完成明细",
              cnum: 2,
              moduleKey: "acceptance-data-appendix",
            },
          ],
        },
        {
          label: "信息披露",
          indent: "五",
          moduleKey: "information-intro",
          children: [],
        },
      ],
    };
  },
  methods: {
    initMethod() {
      this.trimSuccess();
    },
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
};
</script>

<style lang='scss' scoped>
$mainColor: #678ac8;
.directory-page-v8 {
  padding-top: 145px;
  position: relative;
  .directory-t {
    width: 375px;
    height: 76px;
    background: $mainColor;
    border-radius: 11px;
    margin-left: 51px;

    font-weight: bold;
    font-size: 37px;
    color: #ffffff;
    display: flex;
    align-items: center;
    padding-left: 21px;
  }
  .directory-c {
    background-image: var(--background-url);
    height: 585px;
    margin-top: 48px;
    width: 100%;
    background-size: 100% 100%;
    padding-top: 64px;
    padding-left: 51px;
    box-sizing: border-box;
    padding-right: 325px;
  }
  .directory-i {
    padding-bottom: 13px;
    border-bottom: 1px dashed $mainColor;
    margin-bottom: 27px;
  }
  .directory-i-t {
    display: flex;
    align-items: center;
  }
  .directory-i-tl {
    font-weight: 400;
    font-size: 24px;
    color: $mainColor;
    flex: 1;
  }
  .directory-i-tr {
    font-weight: 400;
    font-size: 19px;
    color: #678ac8;
  }
  .directory-i-ci {
    display: flex;
    align-items: center;
  }
  .directory-i-ci-l {
    font-weight: 400;
    font-size: 19px;
    color: $mainColor;
    padding-top: 7px;
    flex: 1;
  }
  .directory-i-ci-r {
    font-weight: 400;
    font-size: 19px;
    color: $mainColor;
  }
  .directory-bottom {
    width: 100%;
    height: 11px;
    background: $mainColor;
  }
}
</style>