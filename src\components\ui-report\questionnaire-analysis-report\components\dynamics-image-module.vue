<template>
  <div class="dynamics-image-module-v6">
    <template v-for="page in pageContent">
      <div
        class="dynamics-image-module-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="dynamics-image-module-page-top"></div>
        <template v-if="page.once">
          <div class="dynamics-image-module-t" v-if="subTitle">{{ subTitle }}</div>
          <div class="dynamics-image-module-ct" v-if="twoTitle">
            <div class="dynamics-image-module-ctl"></div>
            {{ twoTitle }}
          </div>
        </template>
        <div class="dynamics-image-module-c">
          <template v-for="url in imageResult.slice(page.start, page.end)">
            <img
              :src="url"
              class="dynamics-image-module-image"
              :class="{
                'border': !['project-execution-process'].includes(parentUuid)
              }"
              alt=""
              :key="url"
            />
          </template>
        </div>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  data() {
    return {
      parentUuid: '',
      uuidKey: "dynamics-image-module-page",
      subTitle: "",
      twoTitle: '',
      spaceImageCount: 1,
      pageContent: [],
      imageResult: [],
      bgImg:
        this.domainUrl +
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-directory-bg.png",
    };
  },
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    moduleRangeObject: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let imageResult = pageObject.imageResult || [];
      this.subTitle = pageObject.subTitle;
      this.twoTitle = pageObject.twoTitle;
      this.imageResult = imageResult;
      let uuidKey = pageObject.uuidKey;
      let parentUuid = pageObject.parentUuid;
      this.parentUuid = parentUuid;
      if(uuidKey) {
        this.uuidKey = uuidKey;
      }
      if (imageResult.length > 0) {
        this.pageContent.push({
          type: "dynamics-image-module-page",
          once: true,
          uuid: "dynamics-image-module-page_0",
          start: 0,
          end: 1,
        });
      }
      this.sceentInit(
        {
          type: "dynamics-image-module-page",
          once: false,
        },
        imageResult
      );
      await this.$nextTick();
      this.trimSuccess();
    },
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
$borderColor: #70a5d7;
.dynamics-image-module-v6 {
  background: #fff;
  .dynamics-image-module-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 103px 40px 20px;
  }
  .dynamics-image-module-page-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .dynamics-image-module-c {
    margin-top: 43px;
  }
  .dynamics-image-module-image {
    width: 100%;
    max-height: 800px;
    object-fit: contain;
  }
  .dynamics-image-module-image.border {
    border: 2px dashed $borderColor;
  }
  .dynamics-image-module-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-top: 43px;
  }
  .dynamics-image-module-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .dynamics-image-module-t {
    margin-top: 43px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 33px;
    background-size: 100% 100%;
  }
}
</style>