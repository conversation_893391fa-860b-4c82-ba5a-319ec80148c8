<template>
  <el-drawer
    v-if="dialogVisible"
    :title="`${paramsData ? '编辑' : '新增'}服务`"
    :visible.sync="dialogVisible"
    size="1200px"
    :before-close="handleClose"
    center
    append-to-body
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '170px',groupId: 27000 }"
      @getImgUrlObj="getImgUrlObj"
    >
      <template slot="tag" slot-scope="{ row }">
        <tag-input
          v-model="row.value"
          :tags="tags"
          @update:tags="handleTagsUpdate"
        >
        </tag-input>
      </template>
      <template slot="content" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <div style="display: flex;" slot="icon" slot-scope="{ row }">
        <el-button @click="showIconMap = true" size="medium">点击选择图标和背景色</el-button>
        <img style="width: 36px;height: 36px;margin-left: 15px;" v-if="row.value" :src="imgServer + row.value" alt="" srcset="" />
        <iconMap :show="showIconMap" @close="showIconMap = false" @selectIcon="selectIcon"></iconMap>
      </div>
      <div style="display: flex;" slot="color" slot-scope="{ row }">
        <el-button @click="showIconMap = true" size="medium">点击选择图标和背景色</el-button>
        <div style="width: 36px;height: 36px;margin-left: 15px;" v-if="row.value" :style="{backgroundColor:row.value}" ></div>
      </div>
      <template slot="notice" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <div slot="btnList" style="margin-top:50px;">
        <el-button type="primary" size="mini" @click="confirm">确定</el-button>
        <el-button type="primary" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
  </el-drawer>
</template>

<script>
  import {imgServer} from '@/api/config'
  import { getFromData } from "@/utils/index";
  import TagInput from '@/components/tagInput'
  import { serviceInsert,updateServiceData } from '@/api/dmCommunity/accompanyServiceManagement'
  import { getSourceType } from "@/utils/enumeration";
  import {getSource} from '@/views/accompany-doctor/getSource.js'
  import { accompanyserviceclassifyQueryPage } from '@/api/dmCommunity/accompany-doctor'
  import iconMap from './iconMap.vue'
  let defaultFormList = [
    { title: '服务名称', type: 1, id: 'serviceName', value: null, option: [], must: true, maxlength: 60, showWordLimit: true },
    { title: '分类', type: 2, id: 'classifyId', value: null, option: [], must: true, },
    { title: '价格', type: 12, id: 'price', value: null, option: [],must: true},
    // { title: '套餐来源', type: 2, id: 'source', value: null, option: getSourceType(),must: true, },
    { title: '服务描述', type: 1, id: 'description', value: null, option: [], must: true, maxlength: 1000, showWordLimit: true },
    { title: '标签', type: 20, id: 'tag', value: null, option: [] },
    { title: '排序', type: 12, id: 'orderValue', value: null, option: [], },
    { title: '服务内容', type: 20, id: 'content', value: null, option: [],must: true, },
    { title: '预约须知', type: 20, id: 'notice', value: null, option: [],must: true, },
    { title: '列表图', type: 10, id: 'listImg', value: null, option: [],must: true,tip:'建议尺寸 160 * 160px' },
    { title: '详情图', type: 10, id: 'detailImg', value: null, option: [],must: true,tip:'建议尺寸 750 * 520px' },
    { title: '图标', type: 20, id: 'icon', value: null, option: [],must: true,tip:'请选择图标' },
    { title: '背景色', type: 20, id: 'color', value: null, option: [],must: true,tip:'请选择背景色' },
    { title: '启用状态', type: 8, id: 'state', value: 1,activeValue: 1, inactiveValue: 2, inactiveColor: "#C0CCDA", },
  ]
  export default {
    components:{
      TagInput,
      iconMap
    },
    props:{
      show:{
        type:Boolean,
        default:false,
      },
      paramsData:{
        type:Object,
        default:()=>({})
      },
      providerId:{
        type:[Number,String],
        default:null
      },
      tableData:{
        type:Array,
        default:()=>([])
      },
      setupdatecount:{
        type:Number,
        default:0
      },
    },
    data(){
      return{
        imgServer,
        dialogVisible:false,
        formList:JSON.parse(JSON.stringify(defaultFormList)),
        tags:[],
        inputs: [{value:''}], // 用一个数组来跟踪当前有多少个输入框
        showIconMap:false, // 显示iconMap
        classifyList: [], // 分类列表
      }
    },
    watch:{
      show(n){
        this.dialogVisible = n
        if(n) {
          // 打开弹窗时获取分类列表
          this.getClassifyList()
        }
        if(!this.show){
          this.formList = JSON.parse(JSON.stringify(defaultFormList))
          this.tags = []
          this.inputs = [{value:''}]
        }
      },
      setupdatecount(n){
        if(this.paramsData){
          console.log(this.paramsData,'this.paramsData----')
          for(let key in this.paramsData){
            this.formList.forEach((item,index)=>{
              if(item.id == key){
                // 特殊处理富文本字段，检查是否需要转换格式
                if((key === 'content' || key === 'notice') && this.paramsData[key]) {
                  this.formList[index].value = this.convertToHtml(this.paramsData[key]);
                } else {
                  // 其他字段直接赋值
                  this.formList[index].value = this.paramsData[key]
                }
              }
            })
          }
        }
      }
    },
    created(){},
    mounted(){},
    methods:{
      // 获取分类列表
      async getClassifyList() {
        try {
          const res = await accompanyserviceclassifyQueryPage({
            current: 1,
            size: 1000,
            condition: {
              providerId: this.providerId
            }
          });

          if(res.code === 0 && res.data && res.data.records) {
            this.classifyList = res.data.records;

            // 更新分类选项
            const classifyOptions = this.classifyList.map(item => ({
              label: item.name,
              value: item.id
            }));

            // 设置分类下拉框选项
            this.setFormData('classifyId', 'option', classifyOptions);
          }
        } catch (error) {
          console.error('获取分类列表失败', error);
          this.$message.error('获取分类列表失败');
        }
      },

      // 格式转换函数：将纯文本格式（可能包含图片路径）转换为HTML格式
      convertToHtml(text) {
        if (!text) return '';

        // 检查是否包含<p>标签，如果包含，则认为已经是HTML格式
        if (text.indexOf('<p>') !== -1) {
          return text;
        }

        // 提取文本和图片路径
        const imgRegex = /(static\/image\/[^\s]*\.(jpg|png|jpeg|gif))/i;
        const imgMatch = text.match(imgRegex);

        if (imgMatch) {
          // 从文本中移除图片路径
          let textContent = text.replace(imgRegex, '').trim();
          // 将多行文本合并为单行，替换换行符为空格
          textContent = textContent.replace(/\n/g, ' ').trim();

          // 构建HTML内容
          const imgPath = imgMatch[1];
          const imgUrl = `https://file.greenboniot.cn/${imgPath}`;
          return `<p>${textContent} <img class="wscnph" style="width: 100%;" src="${imgUrl}" /></p>`;
        } else {
          // 没有图片路径，只处理文本
          // 将多行文本合并为单行，替换换行符为空格
          let textContent = text.replace(/\n/g, ' ').trim();
          return `<p>${textContent}</p>`;
        }
      },
      selectIcon({icon,color}){
        this.setFormData('icon', "value", icon);
        this.setFormData('color', "value", color);
      },
      deleteInput(index){
        if(this.inputs.length == 1) return
        this.inputs.splice(index, 1);
      },

      addInput() {
        this.inputs.push({ value: '' })
      },

      handleTagsUpdate(updatedTags){
        this.tags = updatedTags
      },

      async confirm(){
        let formParam = getFromData(this.formList)
        if(!formParam) return
        formParam = {
          ...formParam,
          tag:Array.isArray(this.tags) ? this.tags.join(',') : this.tags,
          price:formParam.price * 100,
          state:formParam.state == 2 ? 0 : 1,
          providerId:this.providerId
        }
        formParam.source = await getSource(this.providerId);

        console.log(formParam,'formParam-----')
        let res
        if(this.paramsData){
          formParam.id = this.paramsData.id
          res = await updateServiceData(formParam)
        } else {
          res = await serviceInsert(formParam)
        }
        this.$eltool.successMsg(res.msg)
        this.close('query')
      },

      drugQueryOne(){
        drugQueryOne({ id: this.paramsData.id }).then(res=>{
          if(res.code == 0){
            this.formList.forEach((item, index) => {
              if(['tags'].includes(item.id)){
                // this.formList[index].value = res.data[item.id]
                this.tags = res.data[item.id] ? res.data[item.id].split(",") : []
              } else if(['price'].includes(item.id)){
                this.formList[index].value = res.data[item.id] / 100
              } else {
                this.formList[index].value = res.data[item.id]
              }
            })
          }
        })
      },

      getImgUrlObj({ url, formData }) {
        this.setFormData(formData.id, "value", url);
      },

      setFormData(id, key, value) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            this.formList[i][key] = value;
            return;
          }
        }
      },

      getFormData(id, key) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            return this.formList[i][key];
          }
        }
      },

      handleClose() {
        this.$confirm('数据将不会保存，确认关闭？')
          .then(_ => {
            this.close()
          }).catch(_ => { })
      },
      close(type){
        this.$emit('close',type)
      }
    },
 }
</script>

<style lang='scss' scoped>
  ::v-deep .el-drawer__body {
    padding: 0 24px 24px;
  }
  .tags{
    display: flex;
    flex-direction: column;
    width: 60%;
  }
</style>
