<template>
  <div style="height: 100%">
    <el-table
      size="mini"
      ref="multipleTable"
      v-loading="loading"
      :data="tableData"
      tooltip-effect="dark"
      style="width: 100%"
      border
      stripe
      class="lvTable"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" width="50" label="编号"> </el-table-column>
      <el-table-column
        v-for="(item, index) in titleList"
        :key="index"
        :label="item.label"
        :prop="item.prop"
        :min-width="item.width"
        :show-overflow-tooltip="true"
        :fixed="item.fixed"
      >
        <template slot-scope="scope">
          <div v-if="item.type === 2">
            <!-- 1-草稿 2-回收中 3-停止回收 -->
            <el-button
              v-if="scope.row.runStatus !== 1"
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 1, row: scope.row })"
              >查看内容</el-button
            >
            <el-button
              type="warning"
              size="mini"
              @click="$emit('showTab', { type: 2, row: scope.row })"
              v-permission="['research_edit']"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.runStatus === 1"
              type="success"
              size="mini"
              @click="$emit('showTab', { type: 3, row: scope.row })"
              v-permission="['research_start']"
              >开始</el-button
            >
            <el-button
              v-if="scope.row.runStatus === 3"
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 4, row: scope.row })"
              v-permission="['research_recovery']"
              >重回收</el-button
            >
            <!-- <el-button v-if="[1,2].includes(scope.row.runStatus)" type="danger"  size="mini" @click="$emit('showTab', {type: 5,row: scope.row})">停止</el-button> -->
            <!-- <el-button type="danger"  size="mini" @click="$emit('showTab', {type: 6,row: scope.row})">删除</el-button> -->
            <el-button
              v-if="scope.row.runStatus !== 1"
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 7, row: scope.row })"
              v-permission="['research_audit']"
              >查看记录</el-button
            >

            <el-button
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 10, row: scope.row })"
              >导出报告</el-button
            >

            <el-button
              type="primary"
              size="mini"
              @click="$emit('showTab', { type: 11, row: scope.row })"
              >预览报告</el-button
            >

            <!-- 企业推广 -->
            <template v-if="scope.row.collectionType === 4">
               <el-button
                  type="primary"
                  size="mini"
                  v-clipboard:copy="copyText + '?id=' + scope.row.id"
                  v-clipboard:success="onCopy"
                  v-clipboard:error="onError"
                  >复制路径</el-button
                >

                <el-button
                  type="primary"
                  size="mini"
                  @click="$emit('showTab', { type: 12, row: scope.row })"
                  :loading='scope.row.recommendLoading'
                  >推广码</el-button
                >
            </template>
            <el-button
              type="primary"
              size="mini"
              v-if='[1,4,6].includes(scope.row.collectionType)'
              @click="$emit('showTab', { type: 13, row: scope.row })"
              >验收报告</el-button
            >
           
          </div>
          <div v-else-if="item.prop === 'coverPath'">
            <el-image
              style="width: 100px; height: 100px"
              :src="imgServer + scope.row[item.prop]"
              :preview-src-list="[imgServer + scope.row[item.prop]]"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
          </div>
          <div v-else-if="item.prop === 'time'" class="flex-cloumn">
            <div>{{ scope.row.startTime }}</div>
            <div> - </div>
            <div>{{ scope.row.endTime }}</div>
          </div>
          <template v-else-if="item.prop === 'id'">
            <el-link
              :underline="false"
              type="primary"
              v-clipboard:copy="scope.row[item.prop]"
              v-clipboard:success="onCopy"
              v-clipboard:error="onError"
            >{{ scope.row[item.prop] }}</el-link>
          </template>
          <div v-else>{{ scope.row[item.prop] }}</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
// import { getWidth } from "@/utils/index";
import { imgServer } from "@/api/config";
export default {
  components: {},
  props: {
    tableData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      copyText:"modules/activity/questionnaire/index",
      imgServer,
      titleList: [
        { prop: "demandTitle", label: "关联项目", width: "180px" },
        { prop: "id", label: "活动ID", width: "180px" },
        { prop: "title", label: "标题", width: "220px" },
        { prop: "time", label: "回收时间范围", width: "140px" },
        { prop: "collectionTypeText", label: "类型", width: "100px" },
        // { prop: 'total', label: '统计概况', width: '200px' },
        { prop: "openStatusText", label: "是否内部公开", width: "100px" },
        // { prop: 'coverPath', label: '封面', width: '200px' },
        { prop: "runStatusText", label: "运行状态", width: "100px" },
        { prop: "auditTypeText", label: "审核类型", width: "100px" },

        {
          prop: "",
          label: "操作",
          width: "700px",
          fixed: "right",
          type: 2,
        },
      ],
    };
  },
  watch: {
    tableData(n, o) {
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      });
    },
  },

  methods: {
    onCopy() {
      this.$eltool.successMsg("复制成功");
    },
    onError() {
      this.$eltool.errorMsg("复制失败");
    },
    handleSelectionChange(val) {
      this.$emit("select", val);
    },
    showTab(type, row) {
      this.$emit("showTab", { type, row });
    },
  },
};
</script>

<style lang="scss" scoped>
.flex-cloumn {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 16px;
}
</style>
