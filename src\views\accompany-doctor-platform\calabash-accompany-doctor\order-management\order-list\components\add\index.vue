<template>
  <el-drawer
    :title="showTitleMap[showType]"
    :visible.sync="dialogVisible"
    size="1000px"
    :before-close="handleClose"
    center
  >
    <searchList
      v-if="showType !== 'serviceRecord'"
      :from-data="currentOptions"
      :config="{ size: 24, labelWidth: '150px' }"
      @selectVal="selectVal"
      @getImgUrlObj="getImgUrl"
    >
      <template slot="bookName" slot-scope="{ row }">
        <el-button @click="()=>dialogBookName = true">{{row.value || '请选择就诊人'}}</el-button>
      </template>
      <template slot="tag" slot-scope="{ row }">
        <el-alert :type="row.tagType" class="warningPadding">
          <template slot="title">
            {{row.WarningTip}}
          </template>
        </el-alert>
      </template>
       <div slot="btnList">
        <el-button
          v-for="(item, index) in buttonMap || []"
          :key="index"
          :type="item.type" size="mini" @click="btnCentralized(item.id,item.apiFunc)">{{item.butName}}</el-button>
      </div>
    </searchList>
    <div v-else>
      <div class="btn-group">
        <el-button type="success" size="mini" @click="addSignIn">新增</el-button>
        <!-- <el-button type="danger" size="mini" @click="mulDel">批量删除</el-button> -->
      </div>
      <signInTab
        ref="signInTabRef"
        :tableData="signInRecordList"
        :loading="loading"
        @showTab="showTab"
        @select="select"
      />
      <add :show.sync="addShow" :paramsData="addParamsData" @closeAddSign="loadSignInRecordList" :orderId="this.paramsData.id"/>
    </div>
    <!-- 选择就诊人弹窗 -->
    <el-dialog
      :modal="false"
      title="选择陪诊人"
      :visible.sync="dialogBookName"
      width="50%"
      :before-close="()=>dialogBookName = false"
      :append-to-body="true"
      center>
      <div class="orderListItem">
        <orderListItem :isUnit="true" @rowClick="selectPatient"></orderListItem>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script>
import signInTab from './sign-in-tab'
import add from './add'
import { Message } from 'element-ui'
import { getFromData } from "@/utils/index";
import formMixin from "@/mixin/formMixin";
import options from './options.js';
import {getAccompanyproviderPage,getAccompanyServicePage,getAccompanyemployeePage,accompanyconfigQueryConfig,accompanybookOrderCode,accompanyGetLogList,getAccompanyproviderQueryAll,getAccompanyprovideruserQueryOne,accompanybookDeleteBatch,accompanyserviceclassifyQueryPage,minichannellinkQueryOne} from "@/api/dmCommunity/accompany-doctor.js";
import { insert, update, queryOne } from '@/api/dmCommunity/minichannellink'
import {getShospitalQueryPage} from '@/api/dmCommunity/framerhospital.js'
import {crawlershospitaldeptQuery,crawlershospitaldoctor} from '@/api/hospitalMap.js'
import orderListItem from '@/views/accompany-doctor-platform/calabash-accompany-doctor/drug-order-listItem/index.vue'

export default {
  mixins: [formMixin],
  inject:['getAuditStatusMap'],
  components: {
    signInTab,
    add,
    orderListItem
  },
  props: {
    visible: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    showType:{
      type:String,
      default:''
    },
    paramsData: {
      type: Object,
      default: () => {
        return null;
      },
    },
    tabIndex: {
        type: Number,
        default: 1,
    },
  },
  data() {
    return {
      defaultAppId:'wx436b8e65632f880f',
      file_ctx:this.$env.file_ctx,
      dialogVisible: false,
      currentOptions:null,
      showTitleMap:{},
      buttonMap:[],
      signInRecordList: [],
      loading: false, // 表格loading
      selectArr:[],
      addParamsData:{},
      addShow: false,
      loadFlag:false, //判断是否已经加载了传递来的参数
      dialogBookName:false,
      dto:{}

    };
  },
  async created() {
    // 初始化时禁用服务选择，直到选择了分类
    if(this.showType === 'add' || this.showType === 'cheateOrder') {
      setTimeout(() => {
        const serviceItem = this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', true);
        }
      }, 300);
    }
  },
  computed: {
    mode(){
      return this.getFormData('mode','value')
    },
    provinces(){
      return this.getFormData('provinces','value')
    },
    providerId(){
      return this.getFormData('providerId','value')
    },
    classifyId(){
      return this.getFormData('classifyId','value')
    }
  },
  watch: {
    mode(n){
      console.log('auditStatus',n);
      if(n){
        if(n === 1){
          this.setFormData('employeeId','hidden',false)
        }else{
          this.setFormData('employeeId','hidden',true)
        }
      }
    },
    async provinces(n){
      if(this.paramsData && this.loadFlag){
        this.setFormData('providerId','value',null);
        // this.setFormData('initiateProviderId','value',null);
        this.setFormData('serviceId','value',null);
      }
      if(!n || n.length === 0) {
        this.setFormData('hospitalName','option',[]);
        this.setFormData('providerId','option',[]);
        // this.setFormData('initiateProviderId','option',[]);
        return
      }
      const [province,city] = n
      // 设置服务商
      this.setAccompanyprovider(city);
      // 设置医院
      this.setHospitalName({province,city})
      // 增加传参数据保护逻辑
      if (!this.loadFlag) return  // 初始化时不执行清空操作
      this.setFormData('hospitalName','value',null);
      this.setFormData('deptName','value',null);
      this.setFormData('doctorName','value',null);
    },
    async providerId(n){
      // 设置是否显示服务模式
      this.setStoreShow(n)
      if(!n) return
      let condition = {
        providerId:n,
        city:this.paramsData?.city || (this.provinces && this.provinces[1])
      }
      this.setAccompanyemployee(condition)
      if(this.paramsData && this.loadFlag){
        // 切换服务商时清空分类和服务
        this.setFormData('classifyId','value',null);
        this.setFormData('serviceId','value',null);
        this.setFormData('price','value',null);

        // 禁用服务选择直到选择分类
        const serviceItem = this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', true);
        }
      }
      // 根据服务商ID获取对应的分类列表
      this.getClassifyByProviderId(n);
      // 设置服务
      this.setAccompanyService(n);
    },

    async classifyId(newVal) {
      // 如果分类被清空，需要禁用服务选择
      if(!newVal) {
        const serviceItem = this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', true);
          this.setFormData('serviceId', 'value', null); // 清空服务选择
          this.setFormData('price', 'value', null); // 清空价格
        }
        return;
      }

      // 根据分类ID获取服务列表
      if(this.providerId) {
        this.getServicesByClassifyId(this.providerId, newVal)
      }
    },
    async showType(){
      console.log('this.paramsData',this.paramsData)
      this.loadFlag = false
      let currentOptions = await options(this.showType)
      this.currentOptions = currentOptions.options;
      this.buttonMap = currentOptions.buttonMap;
      this.showTitleMap = currentOptions.showTitleMap;
      if(this.showType === 'orderCode'){
        let {data:{appid,cAppid}} = await getAccompanyprovideruserQueryOne({id:this.paramsData.providerId});
        if(this.paramsData.store) appid = cAppid;
        let routerPath = 'modules/accompany-doctor/service-reservation/index';
        const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // 查询该订单是否有二维码
        let {data:codeData} = await minichannellinkQueryOne({businessId:this.paramsData.id,businessType:4});
        console.log('codeData.qrcodePath',codeData.qrcodePath);

        // 如果有则直接返回
        if(codeData?.qrcodePath){
          this.setFormData('orderCode','value',codeData.qrcodePath);
          loading.close();
        }else{
         // 如果没有则插入参数 渲染二维码
          try{
            let {data} = await insert({
              appid,
              name:'订单二维码',
              businessType:4,
              businessId:this.paramsData.id,
              path:routerPath + '?id=' + this.paramsData.id,
              CustomParameters:`providerId=${this.paramsData.providerId}`
            })
            this.setFormData('orderCode','value',data.qrcodePath);
            loading.close();
          }catch(e){
            console.error('e',e);
            loading.close();
          }
        }
      }
      // 取消订单时，在待派单、待接单、待服务状态下显示退款金额字段
      if(this.showType === 'cancelOrder'){
        // tabIndex: 2=待派单, 3=待接单, 4=待服务
        if(this.tabIndex >= 2 && this.tabIndex <= 4){
          this.setFormData('refundAmount','hidden',false)
        } else {
          this.setFormData('refundAmount','hidden',true)
        }
      }
      if(this.showType === 'cheateOrder'){
        this.setHospitalName(this.paramsData)
        if(this.paramsData && this.paramsData.startTime) {
          // 等待DOM更新和组件初始化完成后再设置值
          setTimeout(() => {
            const startTime = this.paramsData.startTime;
            let endTime = this.paramsData.endTime;
            if(!endTime) {
              const endDate = new Date(startTime);
              endDate.setDate(endDate.getDate() + 1);
              endTime = this.$u.parseTime(endDate, '{y}-{m}-{d} {h}:{i}:{s}');
            }
            this.setFormData('startTimeMap', 'value', [startTime, endTime]);
          }, 100);
        }
      }
      // 服务记录
      if(this.showType === 'serviceRecord'){
        await this.loadSignInRecordList()
      }
      // 更改订单需要隐藏字段的表格集合
      let changeOrderHiddenMap = [2,3,4]
      if(this.showType === 'changeOrder' && changeOrderHiddenMap.indexOf(this.tabIndex) >= 0){
        this.setFormData('provinces','hidden',true)
        this.setFormData('providerId','hidden',true)
        this.setFormData('serviceId','hidden',true)
        this.setFormData('remark','hidden',false)
      }
      if(this.showType === 'changeOrder' && this.tabIndex === 1){
        this.setFormData('serviceId','hidden',true)
      }
      if(!this.paramsData) return
      Object.keys(this.paramsData).map(id=>{
        if(id === 'backupImg'){
          let newBackValue = this.paramsData[id].split(',')
          if(this.paramsData[id] === ''){
            newBackValue = null;
          }
          this.setFormData(id,'value',newBackValue);
          return
        }
        // 退款金额需要从分转换为元显示
        if(id === 'refundAmount' && this.paramsData[id]){
          this.setFormData(id,'value',this.paramsData[id] / 100)
        } else {
          this.setFormData(id,'value',this.paramsData[id])
        }
      })
      if(this.getFormData("price","id")){
        console.log('this.getFormData("serviceId","option")',this.getFormData("serviceId","option"));
        console.log('this.getFormData("serviceId","value")',this.getFormData("serviceId","value"));
        let price = this.getFormData("serviceId","option").filter(e=>e.id === this.getFormData("serviceId","value"))[0]?.price / 100;
        price && this.setFormData('price','value',price);

        // 如果是创建服务单，使用订单中的价格
        if(this.showType === 'cheateOrder' && this.paramsData && this.paramsData.payPrice) {
          const orderPrice = this.paramsData.payPrice / 100;
          this.setFormData('price','value', orderPrice);
        }
      }
      setTimeout(()=>{
        this.loadFlag = true
      },100)
      console.log('this.currentOptions',this.currentOptions);

    },
    visible(n) {
      this.dialogVisible = n;
      if (!n) {
        this.formList.map((item) => {
          this.setFormData(item.id,'value', this.getFormData(item.id,'default') || null)
        });
      }else{
        console.log('this.paramsData',this.paramsData);

      }
    },
    paramsData(n){
      if(n){
      }else{

      }
    }
  },

  methods: {
    async setStoreShow(providerId){
      this.setFormData('store', 'hidden', true)
      this.setFormData('store', 'value', 1)
      if(!providerId) return
      let {data:{appid,cAppid,storeButton,appidButton}} = await getAccompanyprovideruserQueryOne({id:providerId});
        // 如果当前服务商云陪诊和独立小程序开关都没开则不允许选择
        if(!storeButton && !appidButton){
          return this.$message.error('该服务商未开启陪诊服务');
        }
        // 如果当前服务商的appId不等于默认的appId,且服务商开启了云门店码和独立小程序码
        if(appid !== this.defaultAppId && storeButton && appidButton){
          this.setFormData('store', 'hidden', false)
        }
        // 如果当前服务商未开启云陪诊则说明是独立小程序
        if(!storeButton){
          this.setFormData('store', 'value', 0)
        }
    },
    // 选择陪诊人
    selectPatient(row){
      let {name,phone,sex,idcard} = row;
      this.dto = {name,phone,sex,idcard};
      this.setFormData('bookName','value',name);
      this.setFormData('bookPhone','value',phone);
      console.log('选择陪诊人',row,this.currentOptions);

      if(this.patientInformList){
        this.patientInformList[0].value = name
        this.patientInformList[1].value = phone
        this.$emit('changePatientInformation', {bookName:name,bookPhone:phone})
      }
      // 选择结束后关掉弹窗
      this.dialogBookName = false;
    },
    async loadSignInRecordList(){
      let {data} = await accompanyGetLogList({
        businessId:this.paramsData.id,
        businessType:'4'
      })
      this.signInRecordList = data.map(e=>{
        let clockTitleMap = {'1':'签到打卡','4':'接到客户打卡','2':'签出打卡'}
        e.typeText = clockTitleMap[e.type.toString()]
        return e
      });
    },
    selectVal({item}){
      if(item.id === 'hospitalName'){
        let hospitalId = item.option.filter(e=>e.hospitalName === item.value)[0]?.id;
        this.currentHospitalId = hospitalId;
        this.setFormData('deptName','value',null);
        this.setFormData('doctorName','value',null);
        this.setdeptName(hospitalId)
      }
      if(item.id === 'deptName'){
        let deptId = item.option.filter(e=>e.name === item.value)[0]?.id;
        this.setFormData('doctorName','value',null);
        this.getDoctorName({deptId,hospitalId:this.currentHospitalId})
      }
      if(item.id === "classifyId") {
        // 选择分类时，更新服务列表
        if(this.providerId) {
          // 清空当前选择的服务和价格
          this.setFormData('serviceId', 'value', null);
          this.setFormData('price', 'value', null);

          // 根据分类ID筛选服务列表
          this.getServicesByClassifyId(this.providerId, item.value);
        } else {
          this.$message.warning('请先选择服务商');
          this.setFormData('classifyId', 'value', null);
        }
      }
      if(item.id === "serviceId"){
        // 检查是否选择了分类
        const classifyId = this.getFormData('classifyId', 'value');
        if(!classifyId && item.value) {
          this.$message.warning('请先选择服务分类');
          this.setFormData('serviceId', 'value', null);
          return;
        }

        let price = item.option.filter(e=>e.id === item.value)[0]?.price / 100;
        this.setFormData('price','value',price);
      }
    },
    // 设置陪诊师
    async setAccompanyemployee(condition){
      let {data:{records}} = await getAccompanyemployeePage({size:10000,condition})
      records.map(e=>{
        e.label = e.username
        e.value = e.id
      })
      this.setFormData('employeeId','option',records);
    },
    // 设置服务选项
    async setAccompanyService(providerId){
      const {data:{records}} = await getAccompanyServicePage({current:0,size:1000,condition:{providerId}})
      records.map(e=>{
          e.label = e.serviceName
          e.value = e.id
      })
      this.setFormData('serviceId','option',records);
    },
    // 设置服务商选项
    async setAccompanyprovider(city){
      let {data:{defaultProviderId}} = await accompanyconfigQueryConfig({});
      let {data:records} = await getAccompanyproviderQueryAll({city})
      records.map(e=>{
        e.label = e.providerName
        e.value = e.id
      })
      console.log('this.paramsData',this.paramsData);
      // 判断是否是转单来的订单 如果是 则过滤掉转单来的服务商
      if(this.paramsData.transferProviderId){
        records = records.filter(e=>e.id !== this.paramsData.transferProviderId && e.id !== defaultProviderId)
      }
      this.setFormData('providerId','option',records);
      // this.setFormData('initiateProviderId','option',records);
    },
    // 设置医院选项
    async setHospitalName({province,city}){
      let {data:{records:hospitalQuery}} = await getShospitalQueryPage({
          current:0,
          size:1000,
          condition:{
            province:province.split('省')[0],
            city:city.split('市')[0],
          }
      })
      hospitalQuery.map(e=>{
        e.label = e.hospitalName
        e.value = e.hospitalName
      })
      console.log('hospitalQuery',hospitalQuery);
      this.setFormData('hospitalName','option',hospitalQuery);
    },
    async setdeptName(id){
      let {data:getdeptNameMap} = await crawlershospitaldeptQuery({id})
        let deptNameQuery = getdeptNameMap.map(e=>{
          return {...e,label:e.name,value:e.name}
        })
      this.setFormData('deptName','option',deptNameQuery);
    },
    async getDoctorName({hospitalId,deptId}){
        let {data:getDoctorNameMap} = await crawlershospitaldoctor({hospitalId,deptId});
        console.log('getDoctorNameMap',getDoctorNameMap);
        let doctorNameQuery = getDoctorNameMap.map(e=>{
          return {...e,label:e.name,value:e.name}
        })
      this.setFormData('doctorName','option',doctorNameQuery);

      },
    mulDel() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一条数据");
      }

      const ids = this.selectArr.map((item) => {
        return item.id;
      });

      // deleteBatch
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          // this.close();
          accompanybookDeleteBatch(ids.join(',')).then(res => {
            this.$eltool.successMsg(res.msg);
            this.loadSignInRecordList();
          })
        })
        .catch((_) => {});
    },
    addSignIn() {
      this.addShow = true
      this.addParamsData = null
    },
    select(val) {
      this.selectArr = val;
    },
    showTab({ type, row }) {
      switch (type) {
        case 1:
          // 编辑
          this.addParamsData = row
          this.addShow = true
          break;
        case 2:
          // 删除
          this.$confirm('是否确认删除?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then( async () => {
            const res = await signinlogDeleteOne({id: row.id})
            this.$eltool.successMsg(res.msg)
          })
          break
        default:
      }
    },
    getImgUrl({ url, formData }) {
      this.setFormData('backupImg', 'value',url)
    },
    btnCentralized(butId,apiFunc){
      switch (butId) {
        case 'clear':
          this.close(true);
          break;

        default:
          this.confirm(butId,apiFunc)
          break;
      }
    },
    setFormData(id, key, value) {
      if(!this.currentOptions) return
      for (let i = 0; i < this.currentOptions.length; i++) {
        if (this.currentOptions[i].id === id) {
          this.$set(this.currentOptions[i],key,value)
          return true;
        }
      }
    },
    getFormData(id,key){
      if(key) return JSON.parse(JSON.stringify(this.currentOptions?.filter(e=>e.id === id)[0] || {}))?.[key]
      return JSON.parse(JSON.stringify(this.currentOptions.filter(e=>e.id === id)[0] || {}))
    },

    async confirm(butId,apiFunc) {
      const loading = this.$loading({
          lock: true,
          text: 'Loading',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
      let formData = getFromData(this.currentOptions)
      console.log('formData',this.currentOptions,formData);

       // 确保选择了服务分类
       if(['cheateOrder', 'add'].includes(butId) && !formData.classifyId) {
         this.$eltool.errorMsg('请选择服务分类');
         loading.close();
         return;
       }

      if(!formData) return loading.close();
      if(formData.provinces && Array.isArray(formData.provinces)){
        formData.province = formData.provinces[0]
        formData.city = formData.provinces[1]
      }
      if(formData.backupImg && Array.isArray(formData.backupImg)){
        formData.backupImg = formData.backupImg.join(',')
      }
      if(this.showType === 'add'){
        formData.dto = this.dto;
      }
      formData.source = 1;
      if(formData.price){
        formData.price = formData.price * 100;
      }
      // 退款金额转换为分
      if(formData.refundAmount){
        formData.refundAmount = formData.refundAmount * 100;
      }
      try {
        await apiFunc(formData);
      }catch (error) {
        loading.close();
        return
      }
      console.log('formData',formData);
      loading.close();
      Message.success('插入成功')
      this.$emit("close", true);
    },
    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close(true);
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit("close", type);
    },
    // 根据分类ID获取对应服务
    async getServicesByClassifyId(providerId, classifyId) {
      console.log('根据分类ID获取服务列表', providerId, classifyId);
      try {
        // 禁用服务选择直到获取新的列表
        const serviceItem = this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', true);
        }

        if (!classifyId) {
          return; // 如果没有分类ID，则不进行查询
        }

        const {data:{records}} = await getAccompanyServicePage({
          current: 0,
          size: 1000,
          condition: {
            providerId,
            classifyId
          }
        });

        records.map(e => {
          e.label = e.serviceName
          e.value = e.id
        });

        this.setFormData('serviceId', 'option', records);
        this.setFormData('serviceId', 'value', null); // 切换分类时清空服务选择
        this.setFormData('price', 'value', null); // 同时清空价格

        // 启用服务选择
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', false);
        }

        console.log('分类下的服务列表:', records);
      } catch (error) {
        console.error('获取服务列表失败:', error);
        // 出错时也需要启用服务选择
        const serviceItem = this.getFormData('serviceId');
        if(serviceItem) {
          this.$set(serviceItem, 'disabled', false);
        }
      }
    },
    // 在methods部分添加获取服务商分类的方法
    async getClassifyByProviderId(providerId) {
      try {
        console.log('根据服务商ID获取分类列表:', providerId);
        const response = await accompanyserviceclassifyQueryPage({
          current: 1,
          size: 1000,
          condition: {
            providerId: providerId
          }
        });

        const classifyOptions = response.data.records.map(item => ({
          label: item.name,
          value: item.id,
          id: item.id
        })) || [];

        this.setFormData('classifyId', 'option', classifyOptions);
        console.log('获取到的分类数据:', classifyOptions);
      } catch (error) {
        console.error('获取分类数据失败:', error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.orderListItem{
  height: 600px;
  overflow: scroll;
}
.HeadPathBox{
  position: relative;
  .HeadPathBoxLoading{
    position: absolute;
    top: 2px;
    left: 26px;
    height: 25px;
    background: white;
    width: 162px;
    text-align: center;
    color: #888;
    font-size: 13px;
  }
}
.botPrompt{
  font-size: 12px;
  color: #606266;
  margin-left: 150px;
}
.form-box {
  display: flex;
  align-items: center;
  font-size: 14px;

  .form-label {
    width: 100px;
  }


}
.d-flex{
  display: flex;
  align-items: center;
}
.load-more {
  text-align: center;
  color: #888;
  font-size: 13px;
  line-height: 34px;
  cursor: pointer;
}
.empty-text {
  padding: 10px 0;
  margin: 0;
  text-align: center;
  color: #999;
  font-size: 14px;
}
.text {
  font-size: 14px;
}
.item {
  padding: 18px 0;
}
.box-card {
  width: 480px;
}

.form-title {
  color: #000;
  font-size: 16px;
  word-break: break-all;
  margin-bottom: 12px;
}

.line {
  padding-top: 24px;
  width: 100%;
  border-top: 1px solid #ccc;
  margin-top: 12px;
}

::v-deep .el-drawer__body {
  padding: 0 24px 24px;
}
</style>
