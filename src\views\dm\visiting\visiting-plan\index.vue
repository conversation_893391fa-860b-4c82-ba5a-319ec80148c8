<template>
  <div class="content">
    <searchList :from-data="selectFrom" @tabHeight="getTabHeight">
      <el-col slot="btnList-col" :lg="24" style="margin-bottom: 20px">
        <el-button
          size="mini"
          icon="el-icon-search"
          type="primary"
          class="btn"
          @click="search(false)"
          >查询</el-button
        >
        <el-button
          size="mini"
          type="success"
          class="btn"
          @click="add"
          v-permission="['dm_visiting_plam_add']"
          >新增</el-button
        >
        <!-- <el-button
          size="mini"
          :loading="exportVisitDataReportLoading"
          class="btn"
          type="primary"
          @click="exportVisitDataReport"
          >导出数据报告</el-button
        > -->
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="previewAnalysisReportCloud(6)"
          >导出药店类型数据报告(云服务)</el-button
        >
        <el-button
          size="mini"
          :loading="previewDataLoading"
          class="btn"
          type="primary"
          @click="previewVisitDataReport"
          >预览药店类型数据报告</el-button
        >
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="exportVisitDataReportXLXS"
          >导出XLSX数据报告</el-button
        >
        <!-- :loading="exportVisitDataReportLoading" -->
      
        <!-- <el-button
          size="mini"
          class="btn"
          type="primary"
          :loading="exportVisitAnalysisReportLoading"
          @click="exportAnalysisReport"
          >导出分析报告</el-button
        > -->

        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="previewAnalysisReportCloud(4)"
          >导出分析报告(云服务)</el-button
        >

        <!-- <el-button
          size="mini"
          class="btn"
          type="primary"
          :loading="previewVisitAnalysisReportLoading"
          @click="previewAnalysisReport"
          >预览分析报告</el-button
        > -->
        <el-upload
          ref="upload"
          :headers="header"
          class="uploadBtn"
          :action="pharmacyVisitImport"
          :data="{ planId: !$validate.isNull(selectArr) && selectArr[0].id }"
          :auto-upload="true"
          :on-success="drugstoreUploadSuccess"
          :show-file-list="false"
          :before-upload="drugstoreUploadProgress"
          :on-error="drugstoreUploadError"
          style="display: inline-block; margin: 0 10px"
          :disabled="$validate.isNull(selectArr)"
        >
          <el-button
            size="mini"
            type="primary"
            :loading="importRecordShow"
            :disabled="$validate.isNull(selectArr)"
            >导入药店拜访Excel</el-button
          >
        </el-upload>
        <el-button
          size="mini"
          class="btn"
          type="primary"
          @click="downLoadDrugstoreTemplate"
          >下载药店拜访模板</el-button
        >
      </el-col>
    </searchList>

    <el-tabs
      v-model="activeName"
      size="mini"
      type="card"
      @tab-click="search(false)"
    >
      <template v-for="item in tablist">
        <el-tab-pane :label="item.label" :name="item.value" :key="item.value">
          <!-- <div class="tabel" :style="{ height: tabHeight }"> -->
          <div class="tabel">
            <tab
              :table-data="tableData.records"
              :loading="loading"
              @select="select"
              @showTab="showTab"
            />
            <pagination
              :current="current"
              :size="size"
              :total="tableData.total"
              @handleSizeChange="handleSizeChange"
              @handleCurrentChange="handleCurrentChange"
            />
          </div>
        </el-tab-pane>
      </template>
    </el-tabs>

    <exportXlsx
      :filename="exportVisitDataXlsxTitle + '.xlsx'"
      :data="exportVisitDataXlsx"
      :width="'1280px'"
      :hearders="exportVisitDataHeaders"
      :visible="exportVisitDataVisible"
      @close="exportVisitDataVisible = false"
      @query="exportVisitDataVisible = false"
    ></exportXlsx>

    <add :show.sync="addShow" :paramsData="paramsData" />

    <visitingRecordDialog
      :visible="visitingRecordVisible"
      :updatecount="visitingRecordUpdateCount"
      :visitParamsData="visitParamsData"
      @close="closeVisitingRecord"
      :children="true"
    ></visitingRecordDialog>

    <previewPrint
      :visible="previewReportVisible"
      :updatecount="previewUpdateCount"
      :taskId="taskId"
      @close="previewReportVisible = false"
    ></previewPrint>

    <div class="hidden-box">
      <!-- <div> -->
      <exportPDF
        ref="exportPdfRef"
        @compute="computeExport"
        :filename="'拜访计划'"
        :pageSize="pageSize"
        :landscape="true"
        @updateWidth="updateWidth"
      >
        <exportQuestionnaireActivityReport
          @compute="computeSave"
          :updatecount="updatecountExportCount"
          :taskId="taskId"
          :pageSize="pageSize"
          @updateWidth="updateWidth"
        ></exportQuestionnaireActivityReport>
      </exportPDF>
    </div>

    <el-dialog
      title="导出加载中..."
      :visible.sync="exportLoading"
      width="500px"
      :before-close="handleClose"
    >
      <!-- <span></span> -->
      <div class="exportLoadingBox" v-loading="exportLoading"></div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span> -->
    </el-dialog>

    <!-- 导出药店拜访数据 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef3"
        @compute="computeExport"
        :filename="visitFilename + '数据报告'"
        :exportType="2"
        :pageSize="visitDataUpdatecountExportCountPageSize"
        boxId="exportVisitingDataActivityReport"
        @updateWidth="updateDataWidth"
      >
        <exportVisitingDataActivityReport
          @compute="computeDataSaveTwo"
          :updatecount="visitDataUpdatecountExportCount"
          :taskId="visitTaskId"
          :filename="visitFilename"
          :pageSize="visitDataUpdatecountExportCountPageSize"
          @updateWidth="updateDataWidth"
        ></exportVisitingDataActivityReport>
      </exportPDF>
    </div>

    <previewDataReport
      :visible="previewDataVisible"
      :updatecount="previewDataUpdateCount"
      :taskId="visitTaskId"
      @close="previewDataVisible = false"
      :filename="visitFilename"
    ></previewDataReport>

    <!-- 导出药店拜访分析数据 -->
    <div class="hidden-box">
      <exportPDF
        ref="exportPdfRef4"
        @compute="computeExport"
        :filename="visitFilename + '分析报告'"
        :exportType="2"
        :landscape="false"
        :isA4="true"
        :targetPage="questionnaireAnalysisUpdatecountExportCountPageSize2"
        :pageSize="visitAnalysisUpdatecountExportCountPageSize"
        boxId="exportVisitingAnalysisActivityReport"
        @updateWidth="updateDataWidth"
      >
        <exportVisitingAnalysisActivityReport
          @compute="computeAnalysisSaveTwo"
          :updatecount="visitAnalysisUpdatecountExportCount"
          :taskId="visitTaskId"
          :filename="visitFilename"
          :pageSize="visitAnalysisUpdatecountExportCountPageSize"
        ></exportVisitingAnalysisActivityReport>
        <!-- @updateWidth="updateDataWidth" -->
      </exportPDF>
    </div>

    <!-- 预览分析报告 previewAnalysisReport-->
    <previewAnalysisReport
      :visible="previewAnalysisVisible"
      :updatecount="previewAnalysisUpdateCount"
      :taskId="visitTaskId"
      @close="previewAnalysisVisible = false"
      :filename="visitFilename"
    ></previewAnalysisReport>

    <!-- 导入结果 -->
    <import-record-tab
      :show.sync="importRecordShow"
      :tableData="importRecordTabdata"
    ></import-record-tab>

    <reportLog
      :show="showReportLog"
      :pagetype="pagetype"
      :selectIds="selectIds"
      @close="closeReport"
      :landscape="landscape"
      :exportName='exportName'
    ></reportLog>

    <a :href="aHref" ref="aRef" target="_blank"></a>
  </div>
</template>

<script>
import { getFromData } from "@/utils/index";
import { getToken, getTokenName } from "@/utils/auth";
import tab from "./components/table.vue";
import pagination from "@/components/MyPagination";
import add from "./components/add";
import {
  queryPage,
  updatePushStatus,
  queryList,
} from "@/api/dm/visiting/visitingplan";
import { queryList as physicianinfoQueryList } from "@/api/physicianinfo";
import listMixin from "@/mixin/listMixin";
import {
  getVisitingProjectStatus,
  getVisitingPushStatus,
  getVisitingProjectType,
} from "@/utils/enumeration";

import {
  getVisitingplan,
  pharmacyVisitImport,
  // insert,
  // update,
} from "@/api/dm/visiting/visitingplan";

import visitingRecordDialog from "./components/visiting-record-dialog.vue";

import previewPrint from "./components/previewPrint.vue";

import exportQuestionnaireActivityReport from "@/components/exportPDF/template/export-visiting-activity-report.vue";

import exportPDF from "@/components/exportPDF/index";

import exportVisitingAnalysisActivityReport from "@/components/exportPDF/template/export-visiting-analysis-activity-report.vue";
import previewAnalysisReport from "@/views/dm/project/taskAdmin/taskList/components/previewAnalysisReport/index.vue";

import exportVisitingDataActivityReport from "@/components/exportPDF/template/export-visiting-data-activity-report.vue";
import previewDataReport from "@/views/dm/project/taskAdmin/taskList/components/previewDataReport/index.vue";
import importRecordTab from "./components/import-record-tab";

import exportXlsx from "@/components/exportXlsx/index.vue";

import { queryList as queryVisitList } from "@/api/dm/visiting/visitingplanobjectlist.js";

import { getIOSTime } from "@/utils/index";

import { getdrugstoreFeedbackList } from "@/utils/enumeration.js";

import reportLog from "@/views/dm/components/report-log/index.vue";

export default {
  mixins: [listMixin],
  components: {
    tab,
    pagination,
    add,
    visitingRecordDialog,
    previewPrint,
    exportQuestionnaireActivityReport,
    exportPDF,
    previewAnalysisReport,
    exportVisitingAnalysisActivityReport,
    exportVisitingDataActivityReport,
    previewDataReport,
    importRecordTab,
    exportXlsx,
    reportLog,
  },
  data() {
    return {
      // 导出分析报告云服务
      exportName:"",
      selectIds: [],
      showReportLog: false,
      pagetype: 4, // 4 是拜访分析报告
      aHref: "",

      questionnaireAnalysisUpdatecountExportCountPageSize2: {
        width: 595,
        height: 881,
      },
      exportVisitDataXlsx: [],
      exportVisitDataHeaders: [],
      exportVisitDataVisible: false,
      exportVisitDataXlsxTitle: "",

      selectArr: [],
      previewVisitAnalysisReportLoading: false,
      // 预览分析报告
      previewAnalysisVisible: false,
      previewAnalysisUpdateCount: 0,

      exportVisitAnalysisReportLoading: false,
      // 导出分析报告
      visitAnalysisUpdatecountExportCountPageSize: {
        height: 595,
        width: 881,
      },
      visitAnalysisUpdatecountExportCount: 0,

      // 导出数据报告
      exportVisitDataReportLoading: false,
      previewDataLoading: false,

      previewDataVisible: false,
      previewDataUpdateCount: 0,

      visitTaskId: null,
      visitDataUpdatecountExportCount: 0,
      visitDataUpdatecountExportCountPageSize: {
        height: 595,
        width: 881,
      },
      visitFilename: "",

      pageSize: {
        height: 595,
        width: 881,
      },
      exportLoading: false,
      updatecountExportCount: 0,

      previewReportVisible: false,
      taskId: null,
      previewUpdateCount: 0,

      visitParamsData: null,
      planId: null,
      visitingRecordVisible: false,
      visitingRecordUpdateCount: 0,

      activeName: "0",
      tablist: [
        { value: "0", label: "全部" },
        ...getVisitingPushStatus().map((item) => {
          return { ...item, value: item.value + "" };
        }),
      ],
      selectFrom: [
        { title: "标题", id: "id", value: null, type: 2, option: [] },
        // { title: '拜访人', id: 'userId', value: null, type: 2, option: [] },
        {
          title: "拜访类型",
          id: "type",
          value: null,
          type: 2,
          option: getVisitingProjectType(),
        },
        // { title: '发布状态',  id: 'pushStatus',  value: null,  type: 2, option: getVisitingPushStatus()},
        {
          title: "完成状态",
          id: "projectStatus",
          value: null,
          type: 2,
          option: getVisitingProjectStatus(),
        },
        // parseTime(new Date(), '{y}-{m}-{d}') + ' 00:00:00', parseTime(new Date(), '{y}-{m}-{d}') + ' 23:59:59'
        {
          title: "开始时间范围",
          id: "startTimeGroup",
          value: [],
          option: [],
          type: 6,
          defaultTime: ["00:00:00", "23:59:59"],
          dateType: "datetimerange",
        },
        {
          title: "结束时间范围",
          id: "endTimeGroup",
          value: [],
          option: [],
          type: 6,
          defaultTime: ["00:00:00", "23:59:59"],
          dateType: "datetimerange",
        },
      ],
      paramsData: null,
      addShow: false,
      header: {
        [getTokenName()]: getToken(),
        "auth-version": "v2",
      },
      pharmacyVisitImport,
      importRecordShow: false,
      importRecordTabdata: [],
      importLoading: false,
    };
  },
  created() {
    this.search();
    this.physicianinfoQueryList();
    this.queryList();
  },
  watch: {
    importRecordShow() {
      if (!this.importRecordShow) {
        this.importRecordTabdata = [];
      }
    },
    addShow() {
      if (!this.addShow) {
        this.paramsData = null;
        this.search(true);
      }
    },
  },
  methods: {
    closeReport() {
      this.showReportLog = false;
    },
    // 导出分析计划（云服务）
    previewAnalysisReportCloud(pagetype = 4) {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("必须选中一条");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一条");
      }

      if (pagetype === 6) {
        let row = this.selectArr[0];
        if (row.type !== 3) {
          return this.$eltool.errorMsg(
            "关联拜访任务必须是药店类型任务才有此操作"
          );
        }
      }
      this.exportName = this.selectArr[0].title;
      if(pagetype === 4){
        this.exportName += '分析报告'
      }else {
        this.exportName += '数据报告'
      }

      this.selectIds = this.selectArr.map((item) => {
        return {
          businessId: item.id,
        };
      });
      this.pagetype = pagetype;

      this.$nextTick(() => {
        this.showReportLog = true;
      });
    },
    async exportVisitDataReportXLXS() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("请选择要操作条目");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能操作一条数据");
      }

      this.exportVisitDataXlsxTitle = this.selectArr[0].title;

      const res = await queryVisitList({
        // id: this.taskIdResult[i],
        type: this.selectArr[0].type,
        planId: this.selectArr[0].id,
      });
      let type = this.selectArr[0].type;
      let headers = [];

      if (type === 1 || type === 2) {
        headers = [
          { key: "title", title: "标题" },
          { key: "physicianInfoName", title: "拜访人", width: 80 },
          { key: "writeTime", title: "拜访日期", width: 80 },
          { key: "customerName", title: "客户名称", width: 80 },
          { key: "hospitalName", title: "医院", width: 80 },
          { key: "officeCodeText", title: "科室", width: 80 },
          { key: "content", title: "访谈内容", width: 80 },
          { key: "feedbackDesc", title: "客户反馈内容", width: 80 },
          { key: "writeStatus", title: "是否已填写", width: 80 },
          { key: "auditStatusText", title: "审核状态", width: 80 },
        ];
      } else if (type === 3) {
        headers = [
          {
            key: "drugstoreName",
            title: "药店名",
            width: 80,
          },
          {
            key: "drugstoreUserName",
            title: "店员",
            width: 80,
          },
          {
            key: "drugstoreDuration",
            title: "拜访时长(分钟)",
            width: 80,
          },
          {
            key: "drugstoreTransmit",
            title: "传递关键信息",
            width: 120,
          },
          {
            key: "drugstoreFeedbackText",
            title: "客户反馈",
            width: 80,
          },
          {
            key: "terminalDesc",
            title: "终端位置",
            width: 150,
          },
          {
            key: "writeTimeText",
            title: "拜访日期",
            width: 120,
          },
          // {
          //   key: "siteImagePathsText",
          //   title: "现场图片",
          //   width: 120,
          // },
          {
            key: "userInfoName",
            title: "提交人",
            width: 120,
          },
          // {
          //   key: "createTimeText",
          //   title: "提交时间",
          //   width: 120,
          // },
          {
            key: "userInfoPhone",
            title: "手机号码",
            width: 120,
          },
        ];
      }
      this.exportVisitDataHeaders = headers;
      const drugstoreFeedbackArr = getdrugstoreFeedbackList();

      this.exportVisitDataXlsx = res.data.map((item) => {
        item.writeTimeText = getIOSTime(item.writeTime);
        item.drugstoreFeedbackText = this.getEnumText(
          item.drugstoreFeedback,
          drugstoreFeedbackArr
        );
        return item;
      });

      this.exportVisitDataVisible = true;
    },
    downLoadDrugstoreTemplate() {
      window.open(
        `${this.$env.file_ctx}/static/execl/dm/drugstore_visit_template.xlsx`
      );
    },
    drugstoreUploadProgress() {
      if (this.$validate.isNull(this.selectArr) || this.selectArr.length > 1) {
        this.$eltool.warnMsg("请选择且只能选择一条药店拜访计划进行导入");
        return false;
      }
      const isType = this.selectArr.every((item) => item.type === 3);
      if (!isType) {
        this.$eltool.warnMsg("只能选择药店拜访类型进行导入");
        return false;
      }
      this.drugstoreImportLoading = true;
    },
    drugstoreUploadError(res) {
      this.drugstoreImportLoading = false;
      this.$eltool.errorMsg(res.msg);
    },
    drugstoreUploadSuccess(res) {
      this.drugstoreImportLoading = false;
      this.importRecordShow = true;
      this.importRecordTabdata = !this.$validate.isNull(res.data)
        ? res.data
        : [];
      this.$message({
        message: res.msg,
        type: res.code === 0 ? "success" : "warning",
      });
      this.search(true);
    },
    select(val) {
      this.selectArr = val;
    },
    computeAnalysisSaveTwo() {
      this.$refs.exportPdfRef4.loadPdf();
    },
    handleClose() {
      this.exportLoading = false;
    },
    computeExport() {
      this.exportLoading = false;
      this.$eltool.successMsg("导出成功，详情请看下载记录");
    },
    updateDataWidth(width) {
      this.visitDataUpdatecountExportCountPageSize.width = width;
    },
    computeDataSaveTwo() {
      // 导出报告
      this.$refs.exportPdfRef3.loadPdf();
    },
    async exportVisitDataReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      this.exportVisitDataReportLoading = true;

      const res = await getVisitingplan({
        id: item.id,
      });

      const data = res.data;

      this.exportVisitDataReportLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }
      this.exportLoading = true;

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.$nextTick(() => {
        this.visitDataUpdatecountExportCount += 1;
      });
    },
    async previewVisitDataReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      // this.exportLoading = true;
      this.previewDataLoading = true;

      const res = await getVisitingplan({
        id: item.id,
      });

      const data = res.data;
      this.previewDataLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.previewDataVisible = true;
      this.$nextTick(() => {
        this.previewDataUpdateCount += 1;
      });
    },
    async exportAnalysisReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let item = this.selectArr[0];

      this.exportVisitAnalysisReportLoading = true;

      const res = await getVisitingplan({
        id: item.id,
      });

      const data = res.data;

      this.exportVisitAnalysisReportLoading = false;

      if (data.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      this.exportLoading = true;

      this.visitTaskId = data.id;
      this.visitFilename = item.title;

      this.$nextTick(() => {
        this.visitAnalysisUpdatecountExportCount += 1;
      });
      // this.visitAnalysisUpdatecountExportCount += 1
    },
    async previewAnalysisReport() {
      if (this.selectArr.length === 0) {
        return this.$eltool.errorMsg("至少选中一个任务");
      } else if (this.selectArr.length > 1) {
        return this.$eltool.errorMsg("只能选中一个");
      }

      let row = this.selectArr[0];

      if (row.type !== 3) {
        return this.$eltool.errorMsg(
          "关联拜访任务必须是药店类型任务才有此操作"
        );
      }

      // 预览报告
      let location = window.location;
      let params = {
        businessId: row.id,
        pagetype: this.pagetype,
      };
      let str = "";
      for (let key in params) {
        if (str === "") {
          str += key + "=" + params[key];
        } else {
          str += "&" + key + "=" + params[key];
        }
      }

      this.aHref =
        location.origin + location.pathname + "#" + `/servePrint?${str}`;
      this.$nextTick(() => {
        this.$refs.aRef.click();
      });
    },
    handleClose() {
      this.exportLoading = false;
    },
    computeExport() {
      this.exportLoading = false;
    },
    computeSave() {
      this.$refs.exportPdfRef.loadPdf();
    },
    updateWidth() {},

    closeVisitingRecord() {
      this.visitingRecordVisible = false;
    },
    async queryList() {
      const res = await queryList({});
      const data = res.data.map((item) => {
        return {
          ...item,
          value: item.id,
          label: item.title,
        };
      });
      this.setFormData("id", "option", data);
    },
    add() {
      this.addShow = true;
      this.paramsData = null;
    },
    // 获取代理商列表
    async physicianinfoQueryList() {
      // attributionType 1-众包 2-企业内部
      const res = await physicianinfoQueryList({ type: 3, attributionType: 2 });
      const data = res.data.map((item) => {
        return {
          ...item,
          label: item.name,
          value: item.userId,
        };
      });
      this.setFormData("userId", "option", data);
    },
    showTab({ type, row }) {
      switch (type) {
        case 1:
          // 编辑
          this.paramsData = row;
          this.addShow = true;
          break;
        case 2:
          // 启动
          this.$confirm("是否确认启动?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            const res = await updatePushStatus({ id: row.id, pushStatus: 2 });
            this.$eltool.successMsg(res.msg);
            this.search();
          });
          break;
        case 3:
          // 撤销
          this.$confirm("是否确认撤销?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }).then(async () => {
            const res = await updatePushStatus({ id: row.id, pushStatus: 3 });
            this.$eltool.successMsg(res.msg);
            this.search();
          });
          break;
        case 4:
          // 查看记录

          // this.planId = row.id;
          this.visitParamsData = row;
          console.log("this.planId", row);

          this.visitingRecordVisible = true;

          this.$nextTick(() => {
            this.visitingRecordUpdateCount += 1;
            // this.visitingRecordUpdateCount += 1;
          });

          break;

        case 11:
          // 预览报告
          this.previewReportVisible = true;
          this.taskId = row.id;

          this.$nextTick(() => {
            this.previewUpdateCount += 1;
          });

          break;

        case 10:
          // 导出报告
          // this.taskId = row.id;
          // this.exportLoading = true;

          // this.$nextTick(() => {
          //   this.updatecountExportCount += 1;
          // });
          this.selectIds = [
            {
              businessId:row.id,
            }
          ]
          this.pagetype = 9;
          this.exportName = row.title + '拜访计划';
          this.landscape = true;

          this.$nextTick(() => {
            this.showReportLog = true;
          })
          break;
        case 12:
          this.selectIds = [
            {
              businessId: row.id,
            }
          ]
          this.pagetype = 22;
          this.landscape = false;
          this.$nextTick(() => {
            this.showReportLog = true;
          })
          break;
        default:
      }
    },
    search(noreset) {
      if (!noreset) {
        this.size = 10;
        this.current = 1;
      }
      this.loading = true;
      const size = this.size;
      const current = this.current;
      let condition = getFromData(this.selectFrom);
      const [startStartTime = null, endStartTime = null] =
        condition.startTimeGroup || [];
      const [startEndTime = null, endEndTime = null] =
        condition.endTimeGroup || [];
      condition = {
        ...condition,
        pushStatus: this.activeName === "0" ? null : this.activeName,
        endEndTime,
        startEndTime,
        endStartTime,
        startStartTime,
      };

      const getVisitingProjectTypeArr = getVisitingProjectType();

      queryPage({ size, current, condition })
        .then((res) => {
          res.data.records = res.data.records.map((item) => {
            return {
              ...item,
              pushStatusText: this.getEnumText(
                item.pushStatus,
                getVisitingPushStatus()
              ),
              projectStatusText: this.getEnumText(
                item.projectStatus,
                getVisitingProjectStatus()
              ),
              typeText: this.getEnumText(item.type, getVisitingProjectTypeArr),
              time: `${item.startTime || ""} - ${item.endTime || ""}`,
              auditTypeText: item.auditType === 2 ? "多级审核" : "默认审核",
            };
          });
          this.tableData = res.data;
        })
        .then((res) => {
          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
        });
    },
  },
};
</script>

<style scoped lang="scss">
.btn {
  margin-bottom: 10px;
}
.exportLoadingBox {
  width: 300px;
  height: 300px;
  margin: 0 auto;
}
.hidden-box {
  height: 0;
  overflow: hidden;
}
::v-deep .content .search {
  min-height: 0px;
  .main {
    display: flex;
    width: 350px;
    margin-right: 10px;
    .span {
      min-width: 125px;
      line-height: 40px;
    }
  }
}
</style>
