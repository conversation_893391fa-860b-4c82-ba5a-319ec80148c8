<template>
  <div class="project-report-result-v7">
    <template v-for="page in pageContent">
      <div
        class="project-report-result-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="project-report-result-t">
          <div class="project-report-result-tl"></div>
          四、项目验收结果
        </div>
        <div class="project-report-result-c">
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">提交时间</div>
            <div class="project-report-result-ci-r">
              {{ pageObject.createTimeStr }}
            </div>
          </div>
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">验收完成时间</div>
            <div class="project-report-result-ci-r">
              {{ pageObject.reportTimeText }}
            </div>
          </div>
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">任务数量</div>
            <div class="project-report-result-ci-r">
              <div class="project-report-result-ci-r-1">
                {{ pageObject.taskNum }}
              </div>
              <div class="project-report-result-ci-r-2">执行人数</div>
              <div class="project-report-result-ci-r-1">
                {{ pageObject.taskUserNum }}
              </div>
            </div>
          </div>
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">合格任务量</div>
            <div class="project-report-result-ci-r">
              <div class="project-report-result-ci-r-1">
                {{ pageObject.passNum }}
              </div>
              <div class="project-report-result-ci-r-2">不合格任务量</div>
              <div class="project-report-result-ci-r-1">
                {{ pageObject.noPassNum }}
              </div>
            </div>
          </div>
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">验收合格率</div>
            <div class="project-report-result-ci-r">
              {{ pageObject.passRateStr }}
            </div>
          </div>
          <div class="project-report-result-ci">
            <div class="project-report-result-ci-l">验收结果</div>
            <div class="project-report-result-ci-r">
              本次任务量{{ pageObject.taskNum }}条，合格{{
                pageObject.passNum
              }}条，不合格{{ pageObject.noPassNum }}条，验收合格率{{
                pageObject.passRateStr
              }}
            </div>
          </div>
          <!-- <div class="project-report-result-ci" v-if="pagetype === 22">
            <div class="project-report-result-ci-l">验收人信息</div>
            <div class="project-report-result-ci-r">手机号：18300000001</div>
          </div> -->
        </div>
        <pageBottomRectV2
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRectV2>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype", "filePrex"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      subTitle: "",
      pageContent: [
        {
          type: "project-report-result",
          uuid: "project-report-result_0",
          children: [],
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #e7effc;
$mainColorV2: #6D8FCA;
$mainColorV3: #6d8fca;
$mainColorV4: #8cb7df;
.project-report-result-v7 {
  background: #fff;
  font-family: Microsoft YaHei, Microsoft YaHei;
  .project-report-result-page {
    padding-bottom: 50px;
    padding-top: 72px;
    position: relative;
    box-sizing: border-box;
  }
  .project-report-result-t {
    font-weight: bold;
    font-size: 32px;
    color: #678ac8;
    display: flex;
    align-items: center;
  }
  .project-report-result-tl {
    margin-right: 25px;
    width: 19px;
    height: 55px;
    background: $mainColorV2;
  }
  .project-report-result-c {
    padding-top: 37px;
    padding-left: 75px;
    padding-right: 73px;
  }
  .project-report-result-ci:nth-child(1) {
    border-top: 1px solid #777777;
  }
  .project-report-result-ci {
    height: 48px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #777777;
    border-left: 1px solid #777777;
    border-right: 1px solid #777777;
  }
  .project-report-result-ci-l {
    width: 145px;
    min-width: 145px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: $mainColorV2;
    color: #fff;
    height: 100%;
    border-right: 1px solid #777777;
  }
  .project-report-result-ci-r {
    padding: 0 7px;
    font-weight: 400;
    font-size: 17px;
    color: #777777;
    display: flex;
    justify-content: center;
    height: 100%;
    flex: 1;
    box-sizing: border-box;
    align-items: center;
  }
  .project-report-result-ci-r-1 {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .project-report-result-ci-r-2 {
    width: 145px;
    background: $mainColorV2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    height: 100%;
    border-left: 1px solid #777777;
    border-right: 1px solid #777777;
  }
}
</style>