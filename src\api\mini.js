/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'
import env from '@/config/env'
// 用户解绑小程序
export function unBindWxMini(data) {
  return requestV1.postJson(prefix+'/mini/unBindWxMini', data);
}

// 用户解绑小程序
export function unBindApplet(data = {}) {
  return requestV1.postJson(prefix+'/mini/unBindApplet', data);
}

export function createMiniQrCode(data) {

    return env + prefix+'/mini/createMiniQrCode?path=' + data
}


