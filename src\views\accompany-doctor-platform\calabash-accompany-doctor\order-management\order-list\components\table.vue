<template>
  <div style="height: 100%">
    <el-table
      size="mini"
      ref="multipleTable"
      v-loading="loading"
      :data="sortedTableData"
      tooltip-effect="dark"
      style="width: 100%"
      border
      stripe
      v-if="showTable"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <template v-for="(item, index) in mergedColumns">
        <el-table-column
          :key="index"
          :label="item.label"
          :prop="item.prop"
          :min-width="item.width"
          :width="item.realWidth"
          :show-overflow-tooltip="true"
          :fixed="item.fixed"
          :sortable="item.sortable ? 'custom' : false"
          v-if="shouldShowColumn(item,index)"
        >
          <template slot-scope="scope">
            <div v-if="item.type">
              <template v-if="item.type === 'time'">
                {{ scope.row.createTime }}
              </template>
              <template v-if="item.type === 'createTime'">
                {{ scope.row.startTime }} ~ {{ scope.row.endTime }}
              </template>
              <template v-if="item.type === 'payPrice'">
                {{ scope.row.payPrice / 100 }}
              </template>
              <template v-if="item.type === 'yetPayPrice'">
                {{ scope.row.pay ? scope.row.payPrice / 100 : "未支付" }}
              </template>
              <template v-if="item.type === 'imgMap'">
                <template
                  v-for="(imgItem, index) in scope.row.backupImg.split(',')"
                >
                  <img
                    class="imgItem"
                    v-if="imgItem"
                    :key="index"
                    :src="file_ctx + imgItem"
                    alt=""
                  />
                </template>
              </template>
              <template v-if="item.type === 'refundProportion'">
                {{
                  scope.row.refundProportion
                    ? +scope.row.refundProportion * 100 + "%"
                    : "-"
                }}
              </template>
              <template v-if="item.type === 'refundAmount'">
                {{ scope.row.refundAmount ? scope.row.refundAmount / 100 : '-' }}
              </template>
              <template v-if="item.type === 'source'">
                {{ sourceMap[scope.row.source] }}
              </template>
              <template v-if="item.type === 'emits'">
                <el-button
                  size="mini"
                  type="primary"
                  v-for="(item, index) in getEmitBtnMap(scope.row)"
                  :key="index"
                  @click="triggerEmit(item.type, scope.row)"
                >
                  {{ item.title }}
                </el-button>
              </template>
            </div>
            <template v-else>
              <div v-if="item.options">
                {{ getLabel(item.options, scope.row[item.prop]) }}
              </div>
              <div v-else>{{ scope.row[item.prop] }}</div>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tabIndex: {
      type: Number,
      default: 1,
    },
    columns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      file_ctx: this.$env.file_ctx,
      sourceMap: ["", "平台订单", "自营订单"],
      titleList: [
        { prop: "id", label: "订单号", width: "80px" },
        { prop: "source", label: "订单类型", width: "80px", type: "source" },
        { prop: "bookName", label: "就诊人", width: "80px" },
        { prop: "bookPhone", label: "就诊人电话", width: "80px" },
        { prop: "serviceName", label: "服务项目", width: "80px" },
        { prop: "city", label: "就诊城市", width: "80px" },
        { prop: "hospitalName", label: "就诊医院", width: "80px" },
        { prop: "initiateProviderName", label: "下单服务商", width: "80px" },
        { prop: "providerName", label: "接单服务商", width: "80px" },
        { prop: "transferProviderName", label: "转单服务商", width: "80px" },
        { prop: "channelName", label: "渠道链", width: "80px" },

        {
          prop: "backupImg",
          label: "补充内容",
          width: "80px",
          showIndexMap: [0, 1, 2],
          type: "imgMap",
        },

        {
          prop: "serviceTime",
          label: "陪诊时间",
          width: "80px",
          type: "createTime",
          realSortField: "startTime",
          sortable: true,
        },
        {
          prop: "transfer",
          label: "是否转单",
          width: "80px",
          options: [
            { value: 0, label: "否" },
            { value: 1, label: "是" },
          ],
        },
        {
          prop: "payPrice",
          label: "待支付金额",
          width: "80px",
          showIndexMap: [0, 1],
          type: "payPrice",
        },
        {
          prop: "payPrice",
          label: "已支付金额",
          width: "80px",
          showIndexMap: [2, 3, 4, 5, 6, 7],
          type: "yetPayPrice",
        },
        {
          prop: "mode",
          label: "派单模式",
          width: "80px",
          showIndexMap: [3, 4, 5, 6, 7],
        },
        {
          prop: "receiveState",
          label: "接单状态",
          width: "80px",
          options: null,
          showIndexMap: [3],
        },
        {
          prop: "employeeName",
          label: "陪诊师",
          width: "80px",
          showIndexMap: [3, 4, 5, 6, 7],
        },
        {
          prop: "comboPay",
          label: "支付方式",
          width: "80px",
          showIndexMap: [2, 3, 4, 5, 6],
          options: [
            { value: 0, label: "微信支付" },
            { value: 1, label: "套餐支付" },
          ],
        },
        {
          prop: "payTime",
          label: "支付时间",
          width: "80px",
          showIndexMap: [2, 3, 4, 5, 6],
        },
        {
          prop: "comboName",
          label: "套餐名称",
          width: "80px",
          showIndexMap: [2, 3, 4, 5, 6],
        },
        {
          prop: "commentState",
          label: "评价状态",
          width: "80px",
          options: null,
          showIndexMap: [6],
        },
        {
          prop: "star",
          label: "评分等级",
          width: "80px",
          options: null,
          showIndexMap: [6],
        },
        {
          prop: "comment",
          label: "评价内容",
          width: "80px",
          options: null,
          showIndexMap: [6],
        },
        {
          prop: "cancelReason",
          label: "取消原因",
          width: "80px",
          options: null,
          showIndexMap: [7],
        },
        {
          prop: "refundProportion",
          type: "refundProportion",
          label: "退款比例",
          width: "80px",
          options: null,
          showIndexMap: [7],
        },
        {
          prop: "refundAmount",
          label: "退款金额",
          width: "80px",
          options: null,
          showIndexMap: [7],
          type: "refundAmount",
        },

        {
          prop: "createTime",
          label: "创建时间",
          width: "80px",
          sortable: true,
        },
        { prop: "20", label: "操作", width: "340px", type: "emits" },
      ],
      emitBtnMap: [
        {
          title: "查看",
          type: " ",
          showIndexMap: [0, 1, 2, 3, 4, 5, 6, 7],
          type: "check",
        },
        { title: "创建服务单", showIndexMap: [0], type: "cheateOrder" },
        { title: "克隆服务单", showIndexMap: [0], type: "cloneOrder" },
        { title: "订单二维码", showIndexMap: [1], type: "orderCode" },
        { title: "更改订单", showIndexMap: [1, 2, 3, 4], type: "changeOrder" },
        // {title:'更改预约',showIndexMap:[3,4],type:'ChangingReservation'},
        // {title:'派单',showIndexMap:[2],type:'dispatcher'},
        // {title:'重新派单',showIndexMap:[3],type:'againDispatcher'},
        { title: "转单", showIndexMap: [0], type: "transfer", showLogic:row=>row.initiateProviderName === row.providerName},
        { title: "更换陪诊师", showIndexMap: [4], type: "changeDesigners" },
        { title: "服务记录", showIndexMap: [5, 6], type: "serviceRecord" },
        { title: "结束服务", showIndexMap: [5], type: "closeService" },
        {
          title: "取消订单",
          showIndexMap: [2, 3, 4],
          type: "cancelOrder",
        },
      ],
      showTable: true,
      sortedTableData: [], // 确保初始化为数组
      sortProp: "createTime", // 添加默认排序字段
      sortOrder: "descending", // 添加默认排序方式
    };
  },
  computed: {
    // getEmitBtnMap() {
    //   return this.emitBtnMap.filter(
    //     (e) => e.showIndexMap.indexOf(+this.tabIndex) >= 0
    //   );
    // },
    // 合并本地配置与传入配置
    mergedColumns() {
      return this.titleList.map((col) => {
        const setting = this.columns.find((c) => c.prop === col.prop);
        return setting ? { ...col, hidden: setting.hidden } : col;
      });
    },

    // 过滤显示的列
    filteredColumns() {
      return this.mergedColumns.filter(
        (col) => !col.hidden || col.type === "emits" // 强制显示操作列
      );
    },
  },
  watch: {
    tabIndex() {
      this.showTable = false;
      requestAnimationFrame(() => {
        this.showTable = true;
      });
    },
    // 监听表格数据变化
    tableData: {
      immediate: true,
      handler(newVal) {
        this.sortedTableData = [...newVal];
        this.applySorting();
      },
    },
  },
  created() {
    this.applySorting();
  },
  mounted() {},
  inject: ["getOptions"],
  methods: {
    getEmitBtnMap(row) {
      let orderState = +row.orderState - 1
      return this.emitBtnMap.filter(
        (e) => {
          if(e.showLogic && e.providerParameterShowLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.showLogic(row) && e.providerParameterShowLogic(this.currentProvider)
          }
          // 根据按钮本身特性决定是否显示
          if(e.showLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.showLogic(row)
          }
          // 根据服务商判断是否显示
          if(e.providerParameterShowLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.providerParameterShowLogic(this.currentProvider)
          }
          return e.showIndexMap.indexOf(orderState) >= 0
        }
      );
    },
    // 列显示判断
    shouldShowColumn(item,index) {
      // 强制显示操作列
      if (item.type === "emits") return true;


      return (
        !item.hidden &&
        (!item.showIndexMap || item.showIndexMap.includes(+this.tabIndex))
      );
    },
    setFormData(id, key, value) {
      for (let i = 0; i < this.emitBtnMap.length; i++) {
        if (this.emitBtnMap[i].type === id) {
          this.$set(this.emitBtnMap[i], key, value);
          return;
        }
      }
    },
    triggerEmit(type, row) {
      console.log("emitBtnMap", this.getEmitBtnMap);
      this.$emit("editor", { type, row });
    },
    deleteRule(id) {
      console.log("row", id);
      this.$emit("delete", id);
    },
    getLabel(options, value) {
      return options.find((e) => value == e.value)?.label;
    },
    handleSelectionChange(val) {
      this.$emit("select", val);
    },
    // 排序事件处理
    handleSortChange({ prop, order }) {
      if (order) {
        // 获取实际排序字段
        const columnConfig = this.titleList.find((col) => col.prop === prop);
        this.sortProp = columnConfig?.realSortField || prop;
        this.sortOrder = order;
      } else {
        // 重置为默认排序
        this.sortProp = "createTime";
        this.sortOrder = "descending";
      }
      this.applySorting();
    },
    // 执行排序逻辑
    applySorting() {
      // 获取实际排序字段
      const actualSortField =
        this.sortProp === "serviceTime" ? "startTime" : this.sortProp;
      this.sortedTableData = [...this.tableData].sort((a, b) => {
        // 处理默认排序
        if (!actualSortField || !this.sortOrder) {
          return new Date(b.createTime) - new Date(a.createTime);
        }
        // 统一日期解析方法
        const parseDate = (dateStr) => {
          try {
            // 处理时间范围格式
            const cleanStr = dateStr?.split("~")[0] || "";
            return new Date(
              cleanStr
                .replace(/[年月]/g, "-")
                .replace(/日/g, "")
                .trim()
            ).getTime();
          } catch {
            return 0;
          }
        };
        const aValue = parseDate(a[actualSortField]);
        const bValue = parseDate(b[actualSortField]);
        return this.sortOrder === "ascending"
          ? aValue - bValue
          : bValue - aValue;
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.imgItem {
  width: 50px;
  height: 50px;
}
</style>
