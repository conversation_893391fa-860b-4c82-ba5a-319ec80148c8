<template>
  <div
    class="cover-page-v8"
    :style="{
      height: pageSize.height + 'px',
      width: pageSize.width + 'px',
      'background-image': 'url(' + thumbUrl + ')',
    }"
  >
    <div class="cover-top">
      <div class="cover-top-lb"></div>
      项目服务报告
      <div class="cover-top-lb"></div>
    </div>
    <div
      class="cover-center"
      :style="{
        'background-image': 'url(' + thumbCenterUrl + ')',
      }"
    >
      <div
        class="cover-center-t-v2"
        v-if="pagetype === 4"
        v-html="pageObject.title"
      ></div>
      <div class="cover-center-t-v3" v-else-if="pagetype === 3">
        {{ pageObject.title }}
      </div>
      <template v-if="[3,4].includes(pagetype)">
        <div class="cover-center-time-v2">
          {{ pageObject.monthText }}月度服务报告
        </div>
      </template>
    </div>
    <div class="cover-bottom">
      <div class="cover-bottom-t">服务商：{{ pageObject.serviceProvider }}</div>
      <div class="cover-bottom-text">项目方：{{ pageObject.projectParty }}</div>
    </div>
    <reportPageNumber
      v-if="showPaging"
      :uuid="pageUuid"
      :pageNumObject="pageNumObject"
    ></reportPageNumber>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "",
          projectName: "",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    showPaging: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      pageUuid: "styleOneThumb",
      thumbUrl:
        this.domainUrl +
        "image/business/ui-report-image/earth-push-project-report/styleOne/icon-thumb-bg.png",
      thumbCenterUrl:
        this.domainUrl +
        "image/business/ui-report-image/earth-push-project-report/styleOne/icon-thumb-center-bg.png",
    };
  },
  watch: {
    updatecount(n) {
      let data = this.pageObject || {};

      this.trimSuccess();
    },
  },
};
</script>

<style lang='scss' scoped>
$mainColor: #2165a8;
.cover-page-v8 {
  background-size: 100% 100%;
  background-position: top left;
  position: relative;
  .cover-top {
    position: absolute;
    top: 44px;
    right: 47px;
    height: 41px;
    display: inline-flex;
    align-items: center;
    color: $mainColor;
    font-weight: 500;
    font-size: 29px;
  }
  .cover-top-lb {
    width: 45px;
    height: 3px;
    background: $mainColor;
  }
  .cover-center {
    position: absolute;
    top: 351px;
    left: 0;
    right: 0;
    height: 328px;
    padding-right: 47px;
    box-sizing: border-box;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .cover-center-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .cover-center-t {
    font-weight: 400;
    font-size: 48px;
    color: #ffffff;
    text-align: right;
    margin-bottom: 12px;
    padding-top: 109px;
  }
  .cover-center-t-v2 {
    font-weight: 400;
    font-size: 43px;
    color: #ffffff;
    text-align: right;
    margin-bottom: 12px;
    padding-top: 76px;
    font-weight: bold;
  }
  .cover-center-t-v3 {
    text-align: right;
    margin-bottom: 12px;
    padding-top: 76px;
    padding-left: 20px;
    font-weight: bold;
    font-size: 43px;
    color: #FFFFFF;
  }
  .cover-center-time {
    font-weight: bold;
    color: #ffffff;
    text-align: right;
    line-height: 1.5;
    font-size: 22px;
  }
  .cover-center-time-v2 {
    height: 52px;
    text-align: right;
    margin-top: 52px;
    font-weight: 400;
    font-size: 37px;
    color: #FFFFFF;
  }
  .cover-bottom {
    position: absolute;
    top: 786px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
  .cover-bottom-t {
    text-align: center;
    font-size: 27px;
    color: $mainColor;
    text-align: center;
    margin-bottom: 11px;
  }
  .cover-bottom-text {
    text-align: center;
    font-size: 27px;
    color: $mainColor;
    text-align: center;
    margin-bottom: 11px;
  }
}
</style>