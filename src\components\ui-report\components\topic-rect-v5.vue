<template>
  <div
    class="topic-rect-page-v5-v6"
  >
    <template v-for="page in pageContent">
      <div
        class="topic-rect-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="topic-rect-top"></div>
        <template v-if="page.once">
          <div class="topic-rect-ct">
            <div class="topic-rect-ctl"></div>
            {{ page.pageTitle }}
          </div>
        </template>
        <div class="topic-echart">
          <div class="echart-item" :id="page.echatUuid"></div>
        </div>

        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
import topicMixin from "@/components/ui-report/mixins/topic.js";

export default {
  mixins: [toolMixin, topicMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "topic-rect-page",
    },
    renderUpdateCount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      pageHeight: this.pageSize.height - 53,
      boxWidth: this.pageSize.width - 80,
      tableWidth: this.pageSize.width - 80,
      borderWidth: null,
      widthZoom: 1,
      subTitle: "",
      pageContent: [
        {
          type: "topic-rect-page",
          start: 0,
          end: 16,
          once: true,
          headers: [],
          widthZoom: 1,
          borderWidth: null,
          pageTitle: "",
          children: [],
        },
      ],
      // tableHeader: [],
      spaceImageCount: 16,
      formTemplateOptions: [],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
    renderUpdateCount(n) {
      this.initRenderChart();
    },
  },
  methods: {
    async initRenderChart() {
      let pageObject = this.pageObject || {};
      // let topicResult = pageObject.topicResult;
      for (let i = 0; i < this.pageContent.length; i++) {
        let item = this.pageContent[i];
        let seriesDataOne = this.formTemplateOptions.slice(
          item.start,
          item.end
        );
        let chartOne = {
          belongType: 30,
          seriesData: seriesDataOne,
        };
        let chartUuidOne = "#" + item.echatUuid;
        if (seriesDataOne.length > 0) {
          let xAxisOption = {};
          let yAxisOption = {};
          let gridOption = {};
          let itemStyleOption = {};
          if (this.inType === "visit-duration-page") {
            yAxisOption = {
              name: "分钟",
            };
            xAxisOption = {
              name: "人数",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            };
            gridOption = {
              left: 100,
            };
            itemStyleOption = {
              borderRadius: [0, 15, 15, 0],
            };
          } else if (this.inType === "visit-date-page") {
            yAxisOption = {
              name: "日期",
              axisLabel: {
                interval: 0,
              },
            };
            xAxisOption = {
              name: "人数",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            };
            gridOption = {
              left: 100,
            };
            itemStyleOption = {
              borderRadius: [0, 15, 15, 0],
            };
          } else if (this.inType === "number-of-visits-to-pharmacies") {
            xAxisOption = {
              name: "执行人",
              axisLabel: {
                rotate: 45,
                interval: 0,
              },
            };
            yAxisOption = {
              name: "完成药店拜访数量",
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#6e7079", // 轴线颜色
                  width: 1, // 轴线宽度
                  type: "solid", // 实线（默认值，可省略）
                },
              },
            };
          }
          if (
            ["visit-duration-page", "visit-date-page"].includes(this.inType)
          ) {
            this.initBarChart2DV2(chartOne, chartUuidOne, {
              color: "#9dd0ff",
              xAxisOption: xAxisOption,
              yAxisOption: yAxisOption,
              gridOption,
              itemStyleOption,
            });
          } else {
            this.initBarChart2D(chartOne, chartUuidOne, {
              color: "#9dd0ff",
              xAxisOption: xAxisOption,
              yAxisOption: yAxisOption,
            });
          }
        }
      }
    },

    async initRender() {
      let pageObject = this.pageObject || {};
      let item = pageObject.answerReportVo || [];
      if (!item) {
        this.trimSuccess();
      }
      let formTemplateOptions = item.formTemplateOptions || [];
      this.formTemplateOptions = formTemplateOptions.map((item) => {
        return {
          ...item,
          name: item.optionValue,
          value: item.selectOptionNum,
          percent: item.selectOptionProportion,
        };
      });
      this.sceentInit(
        {
          type: "topic-rect-page",
          once: false,
          headers: [],
          widthZoom: 1,
          borderWidth: null,
          children: [],
        },
        formTemplateOptions
      );
      this.pageContent.forEach((item2, itemIdx) => {
        item2.pageTitle = item.title;
      })
      await this.$nextTick();
      this.trimSuccess();
    },
    initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      this.pageHeight = this.pageSize.height - 63;
      this.uuidKey = this.inType;
      if (this.pageContent.length > 0) {
        this.pageContent[0].uuid = this.getUuid();
        this.pageContent[0].echatUuid = this.getUuid();
      }
      this.initRender();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #609bd3;
.topic-rect-page-v5-v6 {
  .topic-rect-page {
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    padding: 165px 40px 20px;
  }
  .topic-rect-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .topic-rect-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-bottom: 33px;
  }
  .topic-rect-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .topic-echart {
    width: 100%;
    height: 816px;
  }
  .echart-item {
    width: 100%;
    height: 100%;
  }
}
</style>