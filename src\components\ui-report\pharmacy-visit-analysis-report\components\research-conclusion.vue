<template>
  <div class="research-conclusion-page-v6">
    <template v-for="page in pageContent">
      <div
        class="research-conclusion-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :class="{
          isPharmacyClass: pagetype === 4 && !['service-company-description'].includes(parentUuid),
        }"
        :key="page.uuid"
      >
        <div class="research-conclusion-page-top"></div>
        <img :src="iconUrl" class="research-conclusion-icon" alt="" />
        <div class="research-conclusion-page-t" v-if="!noTitle">
          {{ subTitle }}
        </div>
        <template v-if="borderOpen">
          <div
            class="research-conclusion-page-c no-border-c"
            :id="page.uuid"
          >
            <p class="p-title">服务公司说明：</p>
            <div class="p-content mgb29">
              本公司以勤勉的职业态度，独立、客观地出具本报告。<br />
              本报告所列数据及信息是本公司合法、公开采集的，但本公司不保证该等信息的准确性或完整性。<br />
              本报告所载的资料、信息、意见及推测只提供给客户作参考之用，并非作为或被视为诊断或唯一性判断。<br />
              分析逻辑基于团队的职业理解，清晰准确地反映了本公司的观点，结论不受任何第三方的授意或影响，特此声明。
            </div>
            <p class="p-title">法律声明：</p>
            <div class="p-content">
              本报告所载内容仅反映于本公司截止发布本报告前的判断。<br />
              在不同时期，本公司可发出与本报告所载数据内容不一致的报告。<br />
              报告及报告中的所有材料的版权归本公司所有，本公司对本报告保留一切权利，未经本公司事先书面授权，本报告的任何部分均不得以任何方式制作任何形式的拷贝、复印件或复制品，或再次分发给任何其他人，或以任何侵犯本公司版权的其他方式使用。<br />
              所有本报告中使用的商标、服务标记及标记均为本公司的商标、服务标记及标记。
            </div>
            <!-- </div> -->
          </div>
        </template>
        <template v-else>
          <div
            class="research-conclusion-page-c"
            :id="page.uuid"
            v-html="page.innerHtml"
          ></div>
        </template>
        <pageBottomRect
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRect>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  data() {
    return {
      parentUuid: '',
      boxHeight: this.pageSize.height - 260,
      borderOpen: false,
      subTitle: "",
      uuidKey: "research-conclusion",
      pageContent: [
        {
          type: "research-conclusion",
          uuid: "research-conclusion-page_0",
          children: [],
          innerHtml: "",
        },
      ],
      iconUrl:
        this.domainUrl +
        "image/business/ui-report-image/earth-push-project-report/styleOne/icon-summary.png",
    };
  },
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "research-conclusion-page",
    },
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    moreAnalysisPage(moreNodes, idx) {
      idx += 1;
      this.pageContent.splice(idx, 0, {
        text: "调研结果分析",
        type: "research-conclusion",
        uuid: this.getUuid(),
        pageContent: {
          contentHtml: "",
        },
      });
      let parentNode = document.createElement("div");
      for (let i = 0; i < moreNodes.length; i++) {
        parentNode.appendChild(moreNodes[i]);
      }
      let innerhtml = parentNode.innerHTML;
      console.log(innerhtml, "innerHtml");
      this.dymamicsTextContent(idx, innerhtml);
    },
    async dymamicsTextContent(cidx, html) {
      this.pageContent[cidx].innerHtml = html;
      await this.$nextTick();
      let uuid = this.pageContent[cidx].uuid;
      let dom = document.getElementById(uuid);
      let children = dom.children;
      let is = true;
      let moreNodes = [];
      console.log("children", children);
      for (let i = 0; i < children.length; i++) {
        let ct = children[i].offsetTop;
        let ch = children[i].offsetHeight;
        console.log("ct=ch", ct, ch);
        if (ct + ch > this.boxHeight) {
          console.log(
            children[i],
            children[i].innerHTML,
            children[i].childNodes
          );
          let childNodes = children[i].childNodes;
          var computedStyle = window.getComputedStyle(children[i]);
          var lineHeight =
            computedStyle.getPropertyValue("line-height").split("px")[0] - 0;
          var clonedElement = children[i].cloneNode();
          let ccount = 0;
          for (let k = 0; k < childNodes.length; k++) {
            ccount += lineHeight;
            if (ccount + ct > this.boxHeight) {
              let b = childNodes[k].cloneNode(true);
              clonedElement.appendChild(b);
              children[i].removeChild(childNodes[k]);
              k -= 1;
            }
          }
          moreNodes.push(clonedElement);
        }
      }
      if (moreNodes.length > 0) {
        console.log("moreNodes", moreNodes);
        this.moreAnalysisPage(moreNodes, cidx);
      }
    },
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle, noTitle, borderOpen, uuidKey, content, parentUuid } = pageObject;
      this.parentUuid = parentUuid;
      this.subTitle = subTitle;
      this.noTitle = noTitle;
      this.borderOpen = borderOpen;
      this.uuidKey = uuidKey;
      if (this.pageContent.length > 0) {
        this.pageContent[0].uuid = uuidKey + "_0";
      }
      if (this.borderOpen) {
        this.trimSuccess();
      } else {
        console.log("pageObject====", pageObject);
        await this.dymamicsTextContent(0, content);
        await this.$nextTick();
        this.trimSuccess();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
$borderColor: #70a5d7;
.research-conclusion-page-v6 {
  background: #fff;
  .research-conclusion-page {
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    padding: 103px 47px 20px 48px;
  }

  .research-conclusion-page-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .research-conclusion-page-t {
    margin-top: 79px;
    height: 59px;
    width: 293px;
    background: var(--title-bg);
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    padding-left: 16px;
    box-sizing: border-box;
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    margin-bottom: 24px;
    background-size: 100% 100%;
  }
  .research-conclusion-page-c {
    // font-weight: 500;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
  }
  .research-conclusion-icon {
    width: 150px;
    height: 150px;
    position: absolute;
    right: 53px;
    top: 95px;
  }
  .no-border-c {
    margin-top: 149px;
    padding: 27px 17px 27px 19px;
    border-radius: 16px;
    position: relative;
    border: 2px dashed $borderColor;
  }
  .p-title {
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    margin-top: 0;
    margin-bottom: 20px;
    line-height: 20px;
  }
  .p-content {
    font-size: 14px;
    color: #777777;
    line-height: 18px;
  }
  .mgb29 {
    margin-bottom: 20px;
  }
  .isPharmacyClass {
    // padding: ;
    padding-left: 63px;
    padding-right: 60px;
    .research-conclusion-page-t {
      margin-top: 112px;
    }
  }
  .span-height {
    height: 331px;
    width: 100%;
  }
  .span-height-positon {
    position: absolute;
    top: 32px;
    left: 21px;
    right: 20px;
  }
}
</style>