<template>
  <div class="updata-mini-program">
    <p>小程序一键推送</p>
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '170px', groupId: 27000 }"
      @getImgUrlObj="getImgUrlObj"
    >
      <template slot="idList">
        <div style="width: 100%;">
          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
          <div style="margin: 15px 0;"></div>
          <el-checkbox-group v-model="formList[0].value" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="miniProgram in miniProgramList" :label="miniProgram.id" :key="miniProgram.id">{{miniProgram.title}}</el-checkbox>
          </el-checkbox-group>
        </div>
      </template>
      <div slot="btnList" style="margin-top: 50px">
        <el-button type="primary" size="mini" @click="confirm">确定</el-button>
      </div>
    </searchList>
  </div>
</template>

<script>
import miniProgramList from './miniProgramList'
import { getFromData } from "@/utils/index";
let publishUrl = 'http://localhost:8888/api/miniprogram/publish';
function getVersion() {
  // 默认格式为 1.0.250724
  let version = '1.0.';
  // 获取当前日期 
  let date = new Date();
  let year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  // 格式化为 20230724
  let formattedDate = `${year}${month.toString().padStart(2, '0')}${day.toString().padStart(2, '0')}`;
  // 格式化为 1.0.20230724
  version = version + formattedDate;
  return version;
}
let defaultFormList = [
  { title: "选择上传的小程序", type: 20, id: "idList", value: [], option: [], must: true },
  { title: "推送描述", type: 1, id: "description", value: '', must: true },
  { title: "版本号", type: 1, id: "version", value: getVersion(), must: true },
];
      //   body: JSON.stringify({
      //   description: '测试同步推送功能',
      //   idList: [1],
      //   version: '1.0.1'
      // })
export default {
  components: {
  },
  props: {},
  data() {
    return {
      miniProgramList,
      formList: JSON.parse(JSON.stringify(defaultFormList)),
      tags: [],
      inputs: [{ value: "" }], // 用一个数组来跟踪当前有多少个输入框
      isIndeterminate:true,
    };
  },
  watch: {},
  created() {},
  async mounted() {
  },
  methods: {
    handleCheckAllChange(val) {
        this.setFormData('idList','value',val ? this.miniProgramList.map(e=>e.id) : [])
        console.log('val',val);
        this.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value) {
      console.log('value',value);
        let checkedCount = value.length;
        this.checkAll = checkedCount === this.miniProgramList.length;
        this.isIndeterminate = checkedCount > 0 && checkedCount < this.miniProgramList.length;
    },

    async confirm() {
      let formParam = getFromData(this.formList);
      if (!formParam) return;
      console.log('formParam',formParam);
      // 去掉description里的空格
      formParam.description = formParam.description.trim();
      // 使用fetch请求publishUrl 请求类型为post
      let loading;
      try {
        // 设置loading
        loading = this.$loading({
          lock: true,
          text: '发布中...',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        const response = await fetch(publishUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formParam)
        });
        // 关闭loading
        loading && loading.close();
        if (response.ok) {
          const result = await response.json();
          console.log('发布成功:', result);
          this.$eltool.successMsg('小程序发布成功');
        } else {
          console.error('发布失败:', response.statusText);
          this.$eltool.errorMsg('小程序发布失败');
        }
      } catch (error) {
        // 关闭loading
        loading && loading.close();
        console.error('请求错误:', error);
        this.$eltool.errorMsg('网络请求失败');
      }
    },

    getImgUrlObj({ url, formData }) {
      this.setFormData(formData.id, "value", url);
    },

    setFormData(id, key, value) {
      for (let i = 0; i < this.formList.length; i++) {
        if (this.formList[i].id === id) {
          this.formList[i][key] = value;
          return;
        }
      }
    },

    getFormData(id, key) {
      for (let i = 0; i < this.formList.length; i++) {
        if (this.formList[i].id === id) {
          return this.formList[i][key];
        }
      }
    },

    handleClose() {
      this.$confirm("数据将不会保存，确认关闭？")
        .then((_) => {
          this.close();
        })
        .catch((_) => {});
    },
    close(type) {
      this.$emit("close", type);
    },
  },
};
</script>

<style lang='scss' scoped>
::v-deep .el-drawer__body {
  padding: 0 24px 24px;
}
.tags {
  display: flex;
  flex-direction: column;
  width: 60%;
}
.updata-mini-program{
  padding: 24px;
  background-color: #fff;

}
</style>