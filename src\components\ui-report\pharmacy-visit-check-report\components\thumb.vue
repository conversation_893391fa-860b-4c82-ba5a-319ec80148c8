<template>
  <div
    class="cover-page-v7"
    :style="{
      height: pageSize.height + 'px',
      width: pageSize.width + 'px',
    }"
  >
    <div class="cover-page-t">
      <div class="cover-page-stl">
        <div class="stl-1"></div>
        <div class="stl-2"></div>
      </div>
      <div class="cover-page-st">项目验收报告</div>
      <div class="cover-page-str">
        <div class="stl-1 str-1"></div>
        <div class="stl-2 str-1"></div>
      </div>
    </div>
    <div class="cover-text-box">
      <div class="cover-text-t">
        {{ pageObject.title }}
      </div>
      <!-- <div class="cover-text-time">验收完成时间：{{ cutTime }}</div> -->
      <div class="cover-text-time">验收完成时间：{{ pageObject.reportTime }}</div>
    </div>
    <div class="serve-tip">项目方：{{ pageObject.projectParty }}</div>
  </div>
</template>

<script>
import { format } from "@/utils/index";
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  data() {
    return {
      pageUuid: "coverV1",
      thumbUrl:
        this.domainUrl +
        "image/business/ui-report-image/personal-push-report/icon-thumb-bg.png",
      cutTime: "",
    };
  },
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "",
          projectName: "",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  watch: {
    updatecount(n) {
      let data = this.pageObject || {};
      this.cutTime =
        this.getTargetTime(format(data.startTime, "YYYY-MM-DD")) +
        " 至 " +
        this.getTargetTime(format(data.endTime, "YYYY-MM-DD"));
      this.trimSuccess();
    },
  },
};
</script>

<style lang='scss' scoped>
$mainColor: #678ac8;
.cover-page-v7 {
  padding-top: 50px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  font-family: Microsoft YaHei, Microsoft YaHei;
  .cover-page-t {
    display: flex;
    align-items: center;
    .cover-page-stl {
      flex: 1;
    }
    .cover-page-st {
      width: 176px;
      height: 39px;
      font-weight: 400;
      font-size: 29px;
      color: #333333;
    }
    .cover-page-str {
      flex: 1;
    }
    .str-1,
    .str-2 {
      margin-right: 0;
      margin-left: auto;
    }
    .stl-1 {
      width: 235px;
      height: 5px;
      background: $mainColor;
      margin-bottom: 5px;
    }
    .stl-2 {
      width: 277px;
      height: 5px;
      background: $mainColor;
    }
  }
  .cover-text-box {
    margin-top: 259px;
    height: 376px;
    width: 100%;
    display: flex;
    flex-direction: column;
    background-image: var(--backgroud-top-bg);
    background-size: 100% 100%;
    padding-top: 79px;
    padding-left: 24px;
    .cover-text-t {
      width: 493px;
      font-weight: bold;
      font-size: 48px;
      color: #ffffff;
    }
    .cover-text-time {
      width: 489px;
      height: 35px;
      font-size: 27px;
      margin-top: 43px;
      color: #ffffff;
      font-weight: 550;
    }
  }
  .serve-tip {
    height: 35px;
    font-weight: bold;
    font-size: 27px;
    color: $mainColor;
    margin-top: 75px;
    display: flex;
    justify-content: center;
  }
}
</style>