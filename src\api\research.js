import requestV1 from '@/common/utils/modules/request'
import env from '@/config/env'
const prefix = '/dm/api/v1'
const prefix2 = '/dm/api/v2'

/**
 * 问卷活动
 */

// 根据ids批量删除
export function deleteBatch(data) {
  return requestV1.deleteForm(`${prefix}/research/delete/batch/${data.ids}`);
}

// 查看总览-表头
export function getOverviewHeader(data) {
  return requestV1.get(`${prefix2}/research/getOverviewHeader`, data);
}

// 查看总览-分页列表查询
export function getOverviewPage(data) {
  return requestV1.postJson(`${prefix}/research/getOverviewPage`, data)
}

// 保存数据
export function insert(data) {
  return requestV1.postJson(`${prefix}/research/insert`, data)
}

// 列表查询
export function queryList(data) {
  return requestV1.get(`${prefix}/research/query/list`, data)
}

// 根据id查询单个
export function queryOne(data,headers = {}) {
  return requestV1.get(`${prefix}/research/query/one`, data,null,headers)
}

// 分页列表查询
export function queryPage(data) {
  return requestV1.postJson(`${prefix}/research/query/page`, data)
}

// 查看答案统计-列表查询
export function queryAnswerStatisticsVo(data) {
  return requestV1.get(`${prefix}/research/queryAnswerStatisticsVo`, data)
}

// 查看表单模板预览
export function queryBySnapshotId(data) {
  return requestV1.get(`${prefix}/research/queryBySnapshotId`, data)
}

// 重回收操作
export function recoveryStatus(data) {
  return requestV1.putJson(`${prefix}/research/recoveryStatus`, data)
}

// 更新数据
export function update(data) {
  return requestV1.putJson(`${prefix}/research/update`, data)
}

// 开始停止操作
export function updateStatus(data) {
  return requestV1.putJson(`${prefix}/research/updateStatus`, data)
}



// 审批添加 /api/v1/
export function researchsavetypeAndAuto(data) {
  return requestV1.putForm(`${prefix}/research/save/typeAndAuto`, data)
}

// 根据任务Id查询用户
export function researchwithTaskquerylist(data) {
  return requestV1.get(`${prefix}/todotasks/query/list/users`, data)
}

// 问卷数据报告
export function researchGetReport(data,headers = {}) {
  return requestV1.get(`${prefix}/research/get/report`, data,null,headers)
}

// 问卷分析报告 
export function researchAnalysisReport(data, headers = {}) {
  return requestV1.get(`${prefix}/research/get/analysis/report`, data, null, headers)
}

// 获取推广码 
export function researchGetQrCode(data) {
  return requestV1.postForm(`${prefix}/research/get/qrCode`, data)
}

// 问卷数据报告
export const researchExportExcel = `${env.ctx}${prefix}/research/export/excel`

// 添加批量关联项目
export function researchBatchBindProject(data) {
  return requestV1.postForm(`${prefix}/research/batch/bind/project`, data)
}

// 评分规则设置-新增
export function researchScorerulesInsert(data) {
  return requestV1.postJson(`${prefix}/scorerules/insert`, data)
}

// 评分规则设置-更新
export function researchScorerulesUpdate(data) {
  return requestV1.putJson(`${prefix}/scorerules/update`, data)
}

// 评分规则设置-根据主键单一查询
export function researchScorerulesQueryOne(data) {
  return requestV1.get(`${prefix}/scorerules/query/one`, data)
}

// 评分规则设置-根据主键单一查询businessId
export function researchScorerulesQueryOneBusinessId(data) {
  return requestV1.get(`${prefix}/scorerules/query/one/businessId`, data)
}

// 健康自测-日统计流水
export function healthQueryDailyStatTotal(data) {
  return requestV1.postJson(`${prefix}/scoreresultlog/statistics`, data)
}

// 健康自测-日统计流水-自测结果
export function queryResultList(data) {
  return requestV1.get(`${prefix}/scoreresultlog/resultList`, data)
}

// 健康自测-日统计流水-分页列表
export function healthQueryPage(data) {
  return requestV1.postJson(`${prefix}/scoreresultlog/query/page`, data)
}

// 健康自测-数据图表-指标数据
export function queryLineChart(data) {
  return requestV1.postJson(`${prefix}/scoreresultlog/researchLineChart`, data)
}

// 健康自测-健康自测Top
export function queryTop(data) {
  return requestV1.postJson(`${prefix}/scoreresultlog/topResearch`, data)
}

// 健康自测-结果占比
export function queryCakeShape(data) {
  return requestV1.postJson(`${prefix}/scoreresultlog/researchCakeShape`, data)
}

// 一键推流
export function researchBatchGenrateTask(data) {
  return requestV1.postJson(`${prefix}/research/batch/generate/task`, data)
}

// 健康自测日统计流水导出
export function scoreresultlogExport(data) {
  return requestV1.download(`${prefix}/scoreresultlog/export`, data, `日统计流水.xlsx`, 'post',{
    'content-type': 'application/json; charset=utf-8'
  })
}

// 根据问卷id, 查询对应的图片数据 
export function commonbusinessdataQueryListData(data,headers = {}) {
  return requestV1.get(`${prefix}/commonbusinessdata/query/list/data`, data,null,headers)
}

// 问卷验收报告 
export function researchReportCheck(data,headers = {}) {
  return requestV1.get(`${prefix}/research/report/check`, data,null,headers)
}

// 问卷和拜访验收报告 
export function reportProjectAcceptance(data,headers = {}) {
  return requestV1.get(`${prefix}/report/project/acceptance`, data,null,headers)
}

// 问卷调研个人报告
export function reportPersonReseach(data,headers = {}) {
  return requestV1.get(`${prefix}/report/person/research`, data,null,headers)
}