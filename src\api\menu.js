/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

let prefix = '/manage/api'

//菜单排序
export function sortMenus(data) {
    return requestV1.postJson(prefix + '/role/sortMenus', data);
}

//编辑菜单
export function editMenu(data) {
    return requestV1.putJson(prefix + '/role/editMenu', data);
}

//管理员重置密码
export function resetPassword(data) {
    return requestV1.postForm(prefix + '/user/resetPassword', data);
}

//添加角色
export function updatePassword(data) {
    return requestV1.postJson(prefix + '/user/updatePassword', data);
}

//添加角色
export function editUser(data) {
    return requestV1.postJson(prefix + '/user/editUser', data);
}

//添加角色
export function updateRoleMenu(data) {
    return requestV1.putJson(prefix + '/role/updateRoleMenu', data);
}

//添加角色
export function deleteRole(data) {
    return requestV1.deleteJson(prefix + '/role/deleteRole', data);
}

//添加角色
export function addRole(data) {
    return requestV1.postJson(prefix + '/role/addRole', data);
}
//获取所有角色
export function listRole(data) {
    return requestV1.postJson(prefix + '/user/batchUpdateUserStatus', data);
}

//批量操作用户
export function batchUpdateUserStatus(data) {
    return requestV1.postJson(prefix + '/user/batchUpdateUserStatus', data);
}

//添加用户
export function addUser(data) {
    return requestV1.postJson(prefix + '/user/addUser', data);
}

//调试查询用户表格分页数据
export function userqQeryPage(data) {
    return requestV1.postJson(prefix + '/user/queryPage', data);
}

//用户所有菜单信息,以树形的数据结构返回
export function listUserMenusForTree(data) {
    return requestV1.postJson(prefix + '/user/listUserMenusForTree', data);
}

export function addAuthNode(data) {
    return requestV1.postJson(prefix + '/role/addAuthNode', data);
}

export function addMenu(data) {
    return requestV1.postJson(prefix + '/role/addMenu', data);
}
export function deleteMenu(data) {
    return requestV1.postJson(prefix + '/role/deleteMenu', data);
}

export function listMenusByRole(data) {
    return requestV1.postJson(prefix + '/role/listMenusByRole', data);
}

export function login(data) {
    return requestV1.postForm('/manage/api/v1/login', data);
}
