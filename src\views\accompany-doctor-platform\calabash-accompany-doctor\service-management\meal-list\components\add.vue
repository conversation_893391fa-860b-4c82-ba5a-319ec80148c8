<template>
  <el-drawer
    :title="`${paramsData ? '编辑' : '新增'}套餐`"
    :visible.sync="dialogVisible"
    size="1200px"
    v-if="dialogVisible"
    :before-close="handleClose"
    center
    append-to-body
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '170px',groupId: 27000 }"
      @getImgUrlObj="getImgUrlObj"
    >
      <template slot="containServe" slot-scope="{ row }">
        <div class="service-content">
          <div class="service-wrapper" v-for="(item,index) in serviceDTOList" :key="index">
            <div class="service-head">
              <div class="service-head-l">
                <span>选择服务：</span>
                <common-list-select
                  v-model="item.serviceId"
                  :query-api="handleServiceQueryPage"
                  value-key="id"
                  label-key="serviceName"
                  name="serviceName"
                  style="width: 70%;"
                  placeholder="请选择服务"
                  @select="(e)=>{handleRumorIdSelect(e,item)}"
                  :defaultParams="{providerId:providerId,}"
                >
                </common-list-select>
              </div>
              <el-button v-if="serviceDTOList.length>1" type="primary" size="mini" @click="handleDelService(index)">-</el-button>
            </div>
            <div class="service-spe">
              <div class="service-spe-l">服务规格：</div>
              <div class="service-spe-r">
                <div class="spe-r-list">
                  <div class="spe-r-item" v-for="(item2,index2) in item.standList" :key="index2">
                    <div class="item-head">
                      <div class="item-head-l">
                        <div class="item-head-num">服务次数：</div>
                        <el-input-number v-model="item2.serviceNum" size="small" @change="handleNumChange" :min="1" label="描述文字"></el-input-number>
                      </div>
                      <div class="item-head-r"><el-button v-if="item.standList.length>1" type="primary" size="mini" @click="handleDelSpe(item,index2)">-</el-button></div>
                    </div>
                    <div class="item-head">
                      <div class="item-head-l">
                        <div class="item-head-num">套餐价格：</div>
                        <el-input-number v-model="item2.comboPrice" size="small" @change="handleMealChange" :min="1" label="描述文字"></el-input-number>
                      </div>
                    </div>
                    <div class="item-head">
                      <div class="item-head-l">
                        <div class="item-head-num" style="opacity: 0;">套餐价格：</div>
                        <!-- <div>提示：原价格￥{{item2.servicePrice}}</div> -->
                        <div>提示：原价格￥ {{item2.serviceNum * item.servicePrice || 0}}</div>
                      </div>
                    </div>
                  </div>
                  <div class="spe-list-add">
                    <el-button type="primary" size="mini" @click="handleAddSpe(item)">添加</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="service-add">
            <el-button type="primary" size="mini" @click="handleAddService()">添加</el-button>
          </div>
        </div>
      </template>
      <template slot="tag" slot-scope="{ row }">
        <tag-input
          v-model="row.value"
          :tags="tags"
          @update:tags="handleTagsUpdate"
        >
        </tag-input>
      </template>
      <template slot="content" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <template slot="notice" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <div slot="btnList" style="margin-top:50px;">
        <el-button type="primary" size="mini" @click="confirm">确定</el-button>
        <el-button type="primary" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
  </el-drawer>
</template>

<script>
  import { getFromData,parseTime } from "@/utils/index";
  import TagInput from '@/components/tagInput'
  import commonListSelect from '@/components/common-list-select.vue'
  import { getExpiredType } from "@/utils/enumeration";
  import { accompanycomboInsert,updateAccompanycomboData,accompanycomboQueryOne,serviceQueryPage,accompanyconfigQueryConfig } from '@/api/dmCommunity/accompany-doctor'
  let defaultFormList = [
    { title: '套餐名称', type: 1, id: 'comboName', value: null, option: [], must: true, maxlength: 60, showWordLimit: true },
    { title: '套餐描述', type: 9, id: 'comboDesc', value: null, option: [],must: true, maxlength: 1000, showWordLimit: true },
    { title: '包含服务', type: 20, id: 'containServe', value: null, option: [], must: true, },
    // { title: '使用有效期', id: 'time', value: null, option: [],type: 6, defaultTime: ['00:00:00', '23:59:59'], dateType: 'datetimerange', must: true },
    { title: '有效期限', type: 2, id: 'expiredType', value: null, option: getExpiredType() },
    { title: '标签', type: 20, id: 'tag', value: null, option: [], },
    { title: '排序', type: 12, id: 'orderValue', value: null, option: [] },
    { title: '套餐内容', type: 20, id: 'content', value: null, option: [],must: true, },
    { title: '购买须知', type: 20, id: 'notice', value: null, option: [],must: true, },
    { title: '列表图', type: 10, id: 'listImg', value: null, option: [],must: true,tip:'建议尺寸 160 * 160px' },
    { title: '详情图', type: 10, id: 'detailImg', value: null, option: [],must: true,tip:'建议尺寸 750 * 520px' },
    { title: '启用状态', type: 8, id: 'state', value: 1,activeValue: 1, inactiveValue: 2, inactiveColor: "#C0CCDA", },
  ]
  export default {
    components:{
      TagInput,
      commonListSelect,
    },
    props:{
      show:{
        type:Boolean,
        default:false,
      },
      paramsData:{
        type:Object,
        default:()=>({})
      },
      providerId:{
        type:[Number,String],
        default:null
      },
      tableData:{
        type:Array,
        default:()=>([])
      },
      setupdatecount:{
        type:Number,
        default:0
      },
    },
    data(){
      return{
        dialogVisible:false,
        formList: JSON.parse(JSON.stringify(defaultFormList)),
        tags:[],
        // serviceQueryPage,
        serviceDTOList:[{
          serviceId:'',
          serviceName:'',
          standList:[
            {serviceId:'',serviceNum:1,comboPrice:1},
          ]
        }],
      }
    },
    watch:{
      show(n){
        this.dialogVisible = n
        if(!this.show){
          this.formList = JSON.parse(JSON.stringify(defaultFormList))
          this.tags = []
          this.serviceDTOList = [{
            serviceId:'',
            serviceName:'',
            standList:[
              {serviceId:'',serviceNum:1,comboPrice:1},
            ]
          }]
        }
      },
      setupdatecount(n){
        if(this.paramsData){
          this.accompanycomboQueryOne()
        }
      }
    },
    created(){},
    mounted(){},
    methods:{
      handleServiceQueryPage(e){
        console.log(e,'e000000')
        return new Promise(async(resolve, reject) => {
          const res = await serviceQueryPage(e)
          resolve(res)
        })
      },
      handleRumorIdSelect(e,item){
        const { id = "",serviceName = "",price = "" } = e || {};
        item.serviceId = id
        item.serviceName = serviceName
        item.servicePrice = price / 100
        this.setFormData("containServe", "value", id);
      },
      // 添加规格的方法
      handleAddSpe(item){
        item.standList.push({serviceId:'',serviceNum:1,comboPrice:1})
      },
      // 删除规格方法
      handleDelSpe(item,index){
        item.standList.splice(index,1)
      },
      // 套餐价格方法
      handleMealChange(){},
      // 服务次数方法
      handleNumChange(){},
      // 添加服务方法
      handleAddService(){
        this.serviceDTOList.push({serviceId:'',serviceName:'',standList:[{serviceId:'',serviceNum:1,comboPrice:1}]})
      },
      // 删除服务方法
      handleDelService(index){
        this.serviceDTOList.splice(index,1)
      },
      deleteInput(index){
        if(this.inputs.length == 1) return
        this.inputs.splice(index, 1);
      },

      addInput() {
        this.inputs.push({ value: '' })
      },

      handleTagsUpdate(updatedTags){
        this.tags = updatedTags
      },

      async confirm(){
        let formParam = getFromData(this.formList)
        if(!formParam) return
        for(let i = 0; i < this.serviceDTOList.length; i++){
          let item = this.serviceDTOList[i]
          if(this.serviceDTOList.length>1 && item.standList.length>1){
            return this.$eltool.errorMsg('多个服务不能同时存在多规格')
          }
        }
        let {data:{defaultProviderId}} = await accompanyconfigQueryConfig({});
        formParam = {
          ...formParam,
          tag:Array.isArray(this.tags) ? this.tags.join(',') : this.tags,
          serviceDTOList:this.serviceDTOList.map(item=>({...item,standList:item.standList.map(item2=>({...item2,comboPrice:item2.comboPrice * 100,serviceId:item.serviceId}))})),
          state:formParam.state == 2 ? 0 : 1,
          providerId:this.providerId,
          source:1,
          providerId:defaultProviderId
        }
        let res
        if(this.paramsData){
          formParam.id = this.paramsData.id
          res = await updateAccompanycomboData(formParam)
        } else {
          res = await accompanycomboInsert(formParam)
        }
        this.$eltool.successMsg(res.msg)
        this.close('query')
      },

      accompanycomboQueryOne(){
        accompanycomboQueryOne({ id: this.paramsData.id }).then(res=>{
          if(res.code == 0){
            this.serviceDTOList = res.data.serviceDTOList.map(item=>({...item,standList:item.standList.map(item2=>({...item2,comboPrice:item2.comboPrice / 100}))}))
            this.content = res.data.content
            this.notice = res.data.notice
            this.formList.forEach((item, index) => {
              if(['tag'].includes(item.id)){
                // this.formList[index].value = res.data[item.id]
                this.tags = res.data[item.id] ? res.data[item.id].split(",") : []
              // } else if (item.id === 'time' && res.data.startTime && res.data.endTime) {
              //   this.formList[index].value = [this.$u.parseTime(res.data.startTime, '{y}-{m}-{d} {h}:{i}:{s}'), this.$u.parseTime(res.data.endTime, '{y}-{m}-{d} {h}:{i}:{s}')]
              } else {
                this.formList[index].value = res.data[item.id]
              }
            })
          }
        })
      },

      getImgUrlObj({ url, formData }) {
        this.setFormData(formData.id, "value", url);
      },

      setFormData(id, key, value) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            this.formList[i][key] = value;
            return;
          }
        }
      },

      getFormData(id, key) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            return this.formList[i][key];
          }
        }
      },

      handleClose() {
        this.$confirm('数据将不会保存，确认关闭？')
          .then(_ => {
            this.close()
          }).catch(_ => { })
      },
      close(type){
        this.$emit('close',type)
      }
    },
 }
</script>

<style lang='scss' scoped>
  ::v-deep .el-drawer__body {
    padding: 0 24px 24px;
    .searchList{
      .el-row :nth-child(3){
        .el-form-item{
          .el-form-item__content{
            .search{
              align-items: flex-start !important;
            }
          }
        }
      }
    }
  }
  .service-content{
    display: flex;
    flex-direction: column;
    .service-wrapper{
      border-radius: 10px;
      margin-top: 10px;
      padding:10px 20px;
      border: 1px solid #dcdfe6;
      .service-head{
        display: flex;
        justify-content: space-between;
        .service-head-l{
          display: flex;
          flex: 1;
        }
      }
      .service-spe{
        display: flex;
        margin-top: 20px;
        .service-spe-r{
          .spe-r-list{
            .spe-r-item{
              background: #f2f2f2;
              padding: 10px;
              border-radius: 5px;
              margin-bottom: 10px;
              .item-head{
                display: flex;
                margin-bottom: 10px;
                .item-head-l{
                  display: flex;
                }
                .item-head-r{
                  margin-left: 10px;
                }
                &:last-child{
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
    .service-add{
      margin-top: 10px;
    }
  }
  .tags{
    display: flex;
    flex-direction: column;
    width: 60%;
  }
</style>
