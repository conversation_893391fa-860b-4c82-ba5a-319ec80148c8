<template>
  <div
    class="pageContent"
    v-loading="pageLoading"
    :style="{
      '--backgroud-top-bg': 'url(' + domainUrl + bgImg + ')',
    }"
  >
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <template v-if="page.type === 'thumb'">
          <thumb
            :pageObject="page.pageObject"
            :updatecount="thumbUpdateCount"
            @success="updateSuccess"
            :showPaging="false"
          ></thumb>
        </template>
        <template v-else-if="page.type === 'directory'">
          <directory
            :pageObject="page.pageObject"
            :updatecount="directoryUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :moduleRangeObject='moduleRangeObject'
          ></directory>
        </template>
      </div>
      <template v-else-if="page.authHeight">
        <information
          :key="index"
          v-if="page.type === 'information'"
          :updatecount="informationUpdateCount"
          :pageObject="page.pageObject"
          @success="updateSuccess"
          @updatePageing="updatePageing"
          :pageNumObject="pageNumObject"
        ></information>
        <projectReportProcess
          :key="index"
          v-if="page.type === 'project-report-process'"
          :updatecount="projectReportProcessUpdateCount"
          :pageObject="page.pageObject"
          @success="updateSuccess"
          @updatePageing="updatePageing"
          :pageNumObject="pageNumObject"
        ></projectReportProcess>
        <projectReportResult
          :key="index"
          v-if="page.type === 'project-report-result'"
          :updatecount="projectReportResultUpdateCount"
          :pageObject="page.pageObject"
          @success="updateSuccess"
          @updatePageing="updatePageing"
          :pageNumObject="pageNumObject"
        ></projectReportResult>
        <acceptanceDataAppendix
          :key="index"
          v-if="page.type === 'acceptance-data-appendix'"
          :updatecount="acceptanceDataAppendixUpdateCount"
          :pageObject="page.pageObject"
          @success="updateSuccess"
          @updatePageing="updatePageing"
          :pageNumObject="pageNumObject"
        ></acceptanceDataAppendix>
        <informationDisclosure
          :key="index"
          v-if="page.type === 'information-disclosure'"
          :updatecount="informationDisclosureUpdateCount"
          :pageObject="page.pageObject"
          @success="updateSuccess"
          @updatePageing="updatePageing"
          :pageNumObject="pageNumObject"
        ></informationDisclosure>
      </template>
    </template>
  </div>
</template>

<script>
import commonMixin from "@/components/ui-report/mixins/common.js";
import thumb from "@/components/ui-report/pharmacy-visit-check-report/components/thumb.vue";
import directory from "@/components/ui-report/pharmacy-visit-check-report/components/directory.vue";
import information from "@/components/ui-report/pharmacy-visit-check-report/components/information.vue";
import projectReportProcess from "@/components/ui-report/pharmacy-visit-check-report/components/project-report-process.vue";
import projectReportResult from "@/components/ui-report/pharmacy-visit-check-report/components/project-report-result.vue";
import acceptanceDataAppendix from "@/components/ui-report/pharmacy-visit-check-report/components/acceptance-data-appendix.vue";
import informationDisclosure from "@/components/ui-report/pharmacy-visit-check-report/components/information-disclosure.vue";
import {
  reportProjectAcceptance,
} from "@/api/research.js";
import { format } from "@/utils/index.js";
import { imgServer } from "@/api/config";

export default {
  mixins: [commonMixin],
  provide() {
    return {
      pageSize: this.pageSize,
      domainUrl: this.domainUrl,
      filePrex: this.filePrex,
      pagetype: this.pagetype,
    };
  },
  components: {
    thumb,
    directory,
    information,
    projectReportProcess,
    projectReportResult,
    acceptanceDataAppendix,
    informationDisclosure,
  },
  props: {
    businessId: {
      type: [Number, String],
      default: null,
    },
    pagetype: {
      type: [Number, String],
      default: "10",
    },
    reportStyle: {
      type: [Number, String],
      default: 1,
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      filePrex:
        "image/business/ui-report-image/pharmacy-visit-check-report/styleOne/",
      // domainUrl: "http://192.168.3.54:49772/",
      bgImg:
        "image/business/ui-report-image/pharmacy-visit-check-report/styleOne/icon-thumb-bg.png",
      domainUrl: imgServer + "static/",
      domainUrlV2: "https://file.greenboniot.cn/",
      pageLoading: false,
      pageContent: [
        {
          text: "封面",
          type: "thumb",
          moduleKey: "thumb",
          pageObject: {},
        },
        {
          text: "目录",
          type: "directory",
          moduleKey: "directory",
          pageObject: {},
        },
        {
          text: "基础信息",
          type: "information",
          moduleKey: 'information',
          authHeight: true,
        },
        {
          text: "项目验收流程",
          type: "project-report-process",
          moduleKey: 'project-execution-process',
          authHeight: true,
        },
        {
          text: "项目验收结果",
          type: "project-report-result",
          moduleKey: 'project-execution-result',
          authHeight: true,
        },
        {
          text: "验收数据附表",
          type: "acceptance-data-appendix",
          moduleKey: 'acceptance-data-appendix',
          authHeight: true,
        },
        {
          text: "信息纰漏",
          type: "information-disclosure",
          moduleKey: 'information-intro',
          authHeight: true,
        },
      ],
      targetCount: 8,
      fixedFieldObject: {
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        productTaskNumber: 0, // 地推团队数量
      },
      thumbUpdateCount: 0,
      directoryUpdateCount: 0,
      informationUpdateCount: 0,
      projectReportProcessUpdateCount: 0,
      projectReportResultUpdateCount: 0,
      acceptanceDataAppendixUpdateCount: 0,
      informationDisclosureUpdateCount: 0,
    };
  },
  methods: {
    stripHtmlTags(html) {
      return html.replace(/<[^>]*>/g, "");
    },
    getDaysInCurrentMonth(str) {
      let arr = str.split("-");
      let year = arr[0];
      let month = arr[1];
      // 设置日期为下个月的第0天，这样它就会回滚到当前月的最后一天
      var nextMonthFirstDay = new Date(year, month, 1);
      var daysInMonth = new Date(nextMonthFirstDay - 1).getDate();
      return daysInMonth;
    },
    async initEchart() {
      const res = await reportProjectAcceptance({
        id: this.businessId,
      });
      const data = res.data;
      console.log("data========", data);
      let thumbObject = {};
      let directoryObject = {};
      let informationObject = {};
      let projectProcessObject = {};
      let projectResultObject = {};
      let projectAppendixObject = {};
      let informationDisclosureObject = {};
      let commonObject = {};
      let startTime = null;
      let endTime = null;
      if (data.research instanceof Object) {
        commonObject.projectParty = this.fixedFieldObject.projectParty;
        commonObject.serviceProvider = data.tenantName;
        commonObject.title = data.research.title;
        commonObject.reportTime = format(
          data.research.reportTime,
          "YYYY-MM-DD"
        );
        let barr = commonObject.reportTime.split('-');
        commonObject.reportTimeText = barr[0] + '年' + barr[1] + '月' + barr[2] + '日'
        let daysInMonth = this.getDaysInCurrentMonth(data.research.taskMonthly);
        let month = data.research.taskMonthly;
        let startTime = month + "-" + "01";
        let endTime = month + "-" + daysInMonth;
        let time = month.split("-").join(" 年 ");
        let startTime2 = time + " 月 01 日";
        let endTime2 = month.split("-")[1] + " 月 " + daysInMonth + " 日";
        commonObject.startTime = startTime;
        commonObject.endTime = endTime;
        commonObject.startTime2 = startTime2;
        commonObject.endTime2 = endTime2;
        informationObject.background = this.stripHtmlTags(
          data.research.background
        );
        commonObject.createTimeStr = data.research.createTime
      }
      commonObject.taskUserNum = data.taskUserNum !== '' ? data.taskUserNum : 0;
      commonObject.taskNum = data.taskNum !== '' ? data.taskNum : 0;
      commonObject.passRateStr = (Number(data.passRate) / 100).toFixed(2) + '%';
      commonObject.noPassNum = data.noPassNum !== '' ? data.noPassNum : 0;
      commonObject.passNum = data.passNum !== '' ? data.passNum : 0;
      if(data.taskUserPassStatisticsList instanceof Object) {
        let taskUserPassStatisticsList = Array.isArray(data.taskUserPassStatisticsList) ? data.taskUserPassStatisticsList : '';
        projectAppendixObject.userList = taskUserPassStatisticsList.map(item => {
          item.noPassRate = 10000 - item.passRate;
          item.noPassRateText = (item.noPassRate / 100).toFixed(2) + '%';
          item.passRateText = (item.passRate / 100).toFixed(2) + '%'
          return item;
        });
      }


      this.pageContent.forEach((item) => {
        switch (item.type) {
          case "thumb":
            item.pageObject = {
              ...thumbObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "directory":
            item.pageObject = {
              ...directoryObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "information":
            item.pageObject = {
              ...informationObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "project-report-process":
            item.pageObject = {
              ...projectProcessObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "project-report-result":
            item.pageObject = {
              ...projectResultObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "acceptance-data-appendix":
            item.pageObject = {
              ...projectAppendixObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
          case "information-disclosure":
            item.pageObject = {
              ...informationDisclosureObject,
              ...commonObject,
              parentUuid: item.type
            };
            break;
        }
      });
      await this.$nextTick();

      this.thumbUpdateCount += 1;
      this.directoryUpdateCount += 1;
      this.informationUpdateCount += 1;
      this.projectReportProcessUpdateCount += 1;
      this.projectReportResultUpdateCount += 1;
      this.acceptanceDataAppendixUpdateCount += 1;
      this.informationDisclosureUpdateCount += 1;
      this.updateSuccess();
    },
  },
  watch: {
    updatecount(n) {
      console.log("updatecount=======", n);
      this.pageLoading = true;
      this.initEchart();
    },
  },
};
</script>

<style lang='scss' scoped>
.everyPage {
  overflow: hidden;
  background: #fff;
}
</style>