<template>
  <div class="preview-box">
    <!-- 问卷分析报告 -->
    <template v-if="pagetype === 3">
      <questionnaireAnalysisReport
        :businessId="businessId"
        :updatecount="updatecount"
        :preview="true"
        :pagetype="pagetype"
        :reportStyle="reportStyle"
        v-if="reportStyle === 2"
      ></questionnaireAnalysisReport>
      <export-questionnaire-analysis-activity-report
        :taskId="businessId"
        :updatecount="updatecount"
        :preview="true"
        v-else
      ></export-questionnaire-analysis-activity-report>
    </template>
    <!-- 拜访分析报告 -->
    <template v-if="pagetype === 4">
      <pharmacyVisitAnalysisReport
        :businessId="businessId"
        :updatecount="updatecount"
        :reportStyle="reportStyle"
        :pagetype="pagetype"
        v-if="reportStyle === 2"
      ></pharmacyVisitAnalysisReport>
      <export-visiting-analysis-activity-report
        :taskId="businessId"
        :updatecount="updatecount"
        :preview="true"
        v-else
      ></export-visiting-analysis-activity-report>
      <!-- pharmacyVisitAnalysisReport -->
    </template>
    <!-- 问卷数据报告 -->
    <template v-if="pagetype === 5">
      <exportQuestionnaireDataActivityReport
        :taskId="businessId"
        :updatecount="updatecount"
        :preview="true"
      ></exportQuestionnaireDataActivityReport>
    </template>
    <!-- 拜访数据报告 -->
    <template v-if="pagetype === 6">
      <exportVisitingDataActivityReportTransverse
        :taskId="businessId"
        :updatecount="updatecount"
        :preview="true"
      ></exportVisitingDataActivityReportTransverse>
    </template>
    <!-- 任务列表征集和问卷报告 -->
    <template v-if="pagetype === 7 || pagetype === 8">
      <exportQuestionnaireActivityReportVerticalScreen
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
      ></exportQuestionnaireActivityReportVerticalScreen>
    </template>
    <!-- 任务列表拜访报告 -->
    <template v-if="pagetype === 9">
      <exportVisitingActivityReportVerticalScreen
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
      ></exportVisitingActivityReportVerticalScreen>
    </template>
    <!-- 项目分析报告 -->
    <template v-if="pagetype === 10">
      <earthPushProjectReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :pagetype="pagetype"
        :reportStyle="reportStyle"
        v-if="reportStyle === 2"
      ></earthPushProjectReport>
      <exportVisitingActivityReportVerticalScreenAccurate
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        v-else
      ></exportVisitingActivityReportVerticalScreenAccurate>
    </template>
    <!-- 项目分析报告 -->
    <template v-if="pagetype === 11">
      <exportVisitingActivityReportVerticalScreenCommon
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :additional="additional"
      ></exportVisitingActivityReportVerticalScreenCommon>
    </template>
    <!-- 小葫芦精准地推个人报告 -->
    <template v-if="pagetype === 12">
      <personalPushReport
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :pagetype="pagetype"
        v-if="reportStyle === 2"
      >
      </personalPushReport>
      <export-questionnaire-activity-personal-report-precise-promotion
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        v-else
      ></export-questionnaire-activity-personal-report-precise-promotion>
    </template>
    <!-- 项目线下推广报告 -->
    <template v-if="pagetype === 13">
      <earthPushProjectReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :pagetype="pagetype"
        :reportStyle="reportStyle"
        v-if="reportStyle === 2"
      ></earthPushProjectReport>
      <onlinePromotionAccurate
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        v-else
      ></onlinePromotionAccurate>
    </template>
    <template v-if="pagetype === 14">
      <personalPushReport
        :taskMonth="businessId"
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :pagetype="pagetype"
        v-if="reportStyle === 2"
      >
      </personalPushReport>
      <onlinePromotionAccuratePersonalReportPrecisePromotion
        :activityId="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        v-else
      ></onlinePromotionAccuratePersonalReportPrecisePromotion>
    </template>
    <template v-if="pagetype === 15">
      <companion-platform-report
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
      ></companion-platform-report>
    </template>
    <!-- <template v-if="[16].includes(pagetype)">
      <onlinePromotionOfPersonalReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
      ></onlinePromotionOfPersonalReport>
    </template> -->
    <template v-if="[16, 17].includes(pagetype)">
      <userActivityPersonalReportUi
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
        v-if="reportStyle === 2"
      ></userActivityPersonalReportUi>
      <userActivityPersonalReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        v-else
      ></userActivityPersonalReport>
    </template>
    <template v-if="[18, 19].includes(pagetype)">
      <userActivityProjectReportUi
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
        :reportStyle="reportStyle"
        v-if="reportStyle === 2"
      ></userActivityProjectReportUi>
      <userActivityProjectReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        v-else
      ></userActivityProjectReport>
    </template>
    <template v-if="[20].includes(pagetype)">
      <sciencePopularizationPersonalReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
      ></sciencePopularizationPersonalReport>
    </template>
    <template v-if="[21].includes(pagetype)">
      <sciencePopularizationProjectReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
      ></sciencePopularizationProjectReport>
    </template>
    <template v-if="[22].includes(pagetype)">
      <pharmacyVisitCheckReport
        :businessId="businessId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
      ></pharmacyVisitCheckReport>
    </template>
    <template v-if="[23].includes(pagetype)">
      <employeeResearchPersonalReport
        :businessId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
      ></employeeResearchPersonalReport>
    </template>
    <template v-if='[24].includes(pagetype)'>
      <pharmacyVisitPersonalReport
        :taskMonth="businessId"
        :taskId="taskId"
        :updatecount="updatecount"
        :businessType="pagetype"
        :pagetype="pagetype"
      ></pharmacyVisitPersonalReport>
    </template>
    <!-- 排查接口返回数据 -->
    <!-- <template v-if="pagetype === 100">
        <checkInterFace 
          :taskMonth='businessId'
          :taskId="taskId"
          :updatecount="updatecount"
          :additional='additional'></checkInterFace>
      </template> -->
  </div>
</template>

<script>
import exportPDF from "@/components/exportPDF/index";
import exportQuestionnaireAnalysisActivityReport from "@/components/exportPDF/template/dm/export-questionnaire-analysis-activity-report-transverse.vue";
import exportVisitingAnalysisActivityReport from "@/components/exportPDF/template/dm/export-visiting-analysis-activity-report-transverse.vue";
import exportQuestionnaireDataActivityReport from "@/components/exportPDF/template/dm/export-questionnaire-data-activity-report-transverse.vue";
import exportVisitingDataActivityReportTransverse from "@/components/exportPDF/template/dm/export-visiting-data-activity-report-transverse.vue";
import exportVisitingActivityReportVerticalScreenAccurate from "@/components/exportPDF/template/dm/export-visiting-activity-report-vertical-screen-accurate.vue";
import exportVisitingActivityReportVerticalScreenCommon from "@/components/exportPDF/template/dm/export-visiting-activity-report-vertical-screen-common.vue";
// 任务列表问卷/征集报告
import exportQuestionnaireActivityReportVerticalScreen from "@/components/exportPDF/template/dm/export-questionnaire-activity-report-vertical-screen.vue";
// 任务列表拜访报告
import exportVisitingActivityReportVerticalScreen from "@/components/exportPDF/template/dm/export-visiting-activity-report-vertical-screen.vue";
// 小葫芦精准地推个人报告
import exportQuestionnaireActivityPersonalReportPrecisePromotion from "@/components/exportPDF/template/dm/export-questionnaire-activity-personal-report-precise-promotion.vue";
// 排查接口返回数据
// import checkInterFace from '@/components/exportPDF/template/checkInterFace.vue'
import onlinePromotionAccurate from "@/components/exportPDF/template/dm-project/online-promotion-accurate.vue";
import onlinePromotionAccuratePersonalReportPrecisePromotion from "@/components/exportPDF/template/dm-project/online-promotion-accurate-personal-report-precise-promotion.vue";
import companionPlatformReport from "@/components/exportPDF/template/companion-platform/companion-platform-report.vue";
import userActivityPersonalReport from "@/components/exportPDF/template/dm/user-activity-personal-report/index.vue";
// 线上/线下用户活动项目报告
import userActivityProjectReport from "@/components/exportPDF/template/dm/user-activity-project-report/index.vue";
// 小葫芦精准地推活动-新UI
import personalPushReport from "@/components/ui-report/personal-push-report/index.vue";
// 小葫芦平台精准地推项目报告-新UI
import earthPushProjectReport from "@/components/ui-report/earth-push-project-report/index.vue";
// 线上推广个人报告-新UI
import onlinePromotionOfPersonalReport from "@/components/ui-report/online-promotion-of-personal-reports/index.vue";
// 用户活动个人报告-新UI
import userActivityPersonalReportUi from "@/components/ui-report/user-activity-personal-report/index.vue";
// 线下/线上用户活动项目报告-新UI
import userActivityProjectReportUi from "@/components/ui-report/user-activity-project-report/index.vue";
// 科普笔记个人报告
import sciencePopularizationPersonalReport from "@/components/ui-report/science-popularization-personal-report/index.vue";
// 科普笔记项目报告
import sciencePopularizationProjectReport from "@/components/ui-report/science-popularization-project-report/index.vue";
// 药店拜访分析报告
import pharmacyVisitAnalysisReport from "@/components/ui-report/pharmacy-visit-analysis-report/index.vue";
// 问卷分析报告
import questionnaireAnalysisReport from "@/components/ui-report/questionnaire-analysis-report/index.vue";
// 药店拜访验收报告
import pharmacyVisitCheckReport from "@/components/ui-report/pharmacy-visit-check-report/index.vue";
// 店员调研验收报告
import employeeResearchPersonalReport from "@/components/ui-report/employee-research-check-report/index.vue";
export default {
  components: {
    exportPDF,
    exportQuestionnaireAnalysisActivityReport,
    exportVisitingAnalysisActivityReport,
    exportQuestionnaireDataActivityReport,
    exportVisitingDataActivityReportTransverse,
    exportQuestionnaireActivityReportVerticalScreen,
    exportVisitingActivityReportVerticalScreen,
    exportVisitingActivityReportVerticalScreenAccurate,
    exportVisitingActivityReportVerticalScreenCommon,
    exportQuestionnaireActivityPersonalReportPrecisePromotion,
    onlinePromotionAccurate,
    onlinePromotionAccuratePersonalReportPrecisePromotion,
    companionPlatformReport,
    userActivityPersonalReport,
    userActivityProjectReport,
    personalPushReport,
    earthPushProjectReport,
    onlinePromotionOfPersonalReport,
    userActivityPersonalReportUi,
    userActivityProjectReportUi,
    sciencePopularizationPersonalReport,
    sciencePopularizationProjectReport,
    pharmacyVisitAnalysisReport,
    questionnaireAnalysisReport,
    pharmacyVisitCheckReport,
    employeeResearchPersonalReport,
    // checkInterFace
  },
  data() {
    return {
      businessId: null,
      taskId: null, // 任务ID
      updatecount: 0,
      pagetype: 1, // 结算记录 2 项目报告
      additional: "",
      reportStyle: 1, // 默认-报告风格
    };
  },
  mounted() {
    try {
      let aDom = document.querySelector("a");
      console.log("aDom", aDom);
      if (aDom) {
        aDom.remove();
      }
    } catch {}
    const query = this.$route.query;
    console.log("query", query, this.$route);
    this.businessId = query.businessId;
    this.taskId = query.taskId;
    this.additional = query.additional || "";
    if (query.pagetype && query.pagetype != "") {
      this.pagetype = query.pagetype - 0;
    }
    if (query.reportStyle && query.reportStyle !== "") {
      this.reportStyle = Number(query.reportStyle) || 1;
    }
    this.$nextTick(() => {
      this.updatecount += 1;
    });
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  // height: 100vh;
}

.loader-item {
  width: 20px;
  height: 20px;
  background-color: #333;
  border-radius: 50%;
  margin: 5px;
  opacity: 0;
  animation: appear 3s linear infinite;
}

@keyframes appear {
  0% {
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  50% {
    opacity: 1;
  }
  70% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}
.progress-box {
  width: 100%;
  // width: 900px;
  // height: 100px;
  // margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;

  .progress {
    width: 900px;
  }
  .progress-txt {
    height: 100px;
    display: flex;
    align-items: center;
    font-size: 24px;
    justify-content: center;
  }
}
.hidden-box {
  height: 0;
  width: 0;
  overflow: hidden;
}
.preview-box {
  display: flex;
  min-width: 100%;
  justify-content: center;
  background: rgb(243, 243, 243);
  min-height: 100vh;
  // padding-bottom: 100px;
}
</style>