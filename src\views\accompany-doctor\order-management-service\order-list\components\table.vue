<template>
  <div style="height: 100%">
    <el-table
      size="mini"
      ref="multipleTable"
      v-loading="loading"
      :data="sortedTableData"
      tooltip-effect="dark"
      style="width: 100%"
      border
      stripe
      v-if="showTable"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <template v-for="(item, index) in mergedColumns">
        <el-table-column
          :key="index"
          :label="item.label"
          :prop="item.prop"
          :min-width="item.width"
          :width="0"
          :show-overflow-tooltip="true"
          :fixed="item.fixed"
          :sortable="item.sortable ? 'custom' : false"
          v-if="shouldShowColumn(item)"
        >
          <template slot-scope="scope">
            <div v-if="item.type">
              <template v-if="item.type === 'time'">
                {{ scope.row.createTime }}
              </template>
              <template v-if="item.type === 'orderState'">
                {{ orderStateList[scope.row.orderState] }}
              </template>
              <template v-if="item.type === 'createTime'">
                {{ scope.row.startTime }} ~ {{ scope.row.endTime }}
              </template>
              <template v-if="item.type === 'payPrice'">
                {{ scope.row.payPrice / 100 }}
              </template>
              <template v-if="item.type === 'insurePrice'">
                {{ scope.row.insurePrice / 100 }}
              </template>
              <template v-if="item.type === 'refundInsurePrice' && (scope.row.orderState === 7 || scope.row.orderState === -1)">
                {{ scope.row.insurePrice / 100 }}
              </template>
              <template v-if="item.type === 'allPrice'">
                {{ scope.row.payPrice / 100 + scope.row.insurePrice / 100 }}
              </template>
              <template v-if="item.type === 'yetPayPrice'">
                {{ scope.row.pay ? scope.row.payPrice / 100 : "未支付" }}
              </template>
              <template v-if="item.type === 'mode'">
                {{ scope.row.mode === 1 ? "指定派单" : "抢单" }}
              </template>
              <template v-if="item.type === 'source'">
                {{ sourceMap[scope.row.source] }}
              </template>
              <template v-if="item.type === 'receiveState'">
                {{ ["", "待接单", "已接单", "已拒绝"][scope.row.receiveState] }}
              </template>
              <template v-if="item.type === 'commentState'">
                {{ ["", "待评价", "已评价"][scope.row.commentState] }}
              </template>
              <template v-if="item.type === 'insureStatus'">
                {{ ['待生效','保障中','已完成','已退保'][scope.row.commentState] }}
              </template>
              <template v-if="item.type === 'imgMap'">
                <template
                  v-for="(imgItem, index) in scope.row.backupImg.split(',')"
                >
                  <img
                    class="imgItem"
                    v-if="imgItem"
                    :key="index"
                    :src="file_ctx + imgItem"
                    alt=""
                  />
                </template>
              </template>
              <template v-if="item.type === 'refundProportion'">
                {{
                  scope.row.refundProportion
                    ? +scope.row.refundProportion * 100 + "%"
                    : "-"
                }}
              </template>
              <template v-if="item.type === 'refundAmount'">
                {{ scope.row.refundAmount ? scope.row.refundAmount / 100 : '-' }}
              </template>
              <template v-if="item.type === 'refundReason'">
                {{ scope.row.refundReason || '-' }}
              </template>
              <template v-if="item.type === 'emits'">
                <div
                  class="buttonsBox"
                  v-for="(butItem, index) in getEmitBtnMap(scope.row)"
                  :key="index"
                >
                  <el-button
                    size="mini"
                    type="primary"
                    v-if="
                      scope.row.providerId === providerId ||
                      butItem.type === 'check'
                    "
                    @click="triggerEmit(butItem.type, scope.row)"
                  >
                    {{ butItem.title }}
                  </el-button>
                </div>
              </template>
            </div>
            <template v-else>
              <div v-if="item.options">
                {{ getLabel(item.options, scope.row[item.prop]) }}
              </div>
              <div v-else>{{ scope.row[item.prop] }}</div>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
  const orderStateList = [
    '',
    '待接入',
    '待支付',
    '待派单',
    '待接单',
    '待服务',
    '服务中',
    '已完成',
    '已取消',
  ]
export default {
  props: {
    tableData: {
      type: Array,
      default: function () {
        return [];
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tabIndex: {
      type: Number,
      default: 1,
    },
    providerId: {
      default: null,
    },
    columns: {
      type: Array,
      default: () => [],
    },
    currentProvider:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      orderStateList,
      file_ctx: this.$env.file_ctx,
      sourceMap: ["", "平台订单", "自营订单"],
      titleList: [
        { prop: "id", label: "订单号", width: "80px" },
        { prop: "combineOrderId", label: "联合订单号", width: "80px" },
        { prop: "orderState", label: "订单状态", width: "80px" ,type:'orderState'},
        { prop: "source", label: "订单类型", width: "80px", type: "source" },
        { prop: "bookName", label: "就诊人", width: "80px" },
        { prop: "bookPhone", label: "就诊人电话", width: "80px" },
        { prop: "payPhone", label: "付款人手机号码", width: "80px" , showIndexMap: [-1,3, 4, 5, 6,7,8]},
        { prop: "serviceName", label: "服务项目", width: "80px" },
        { prop: "city", label: "就诊城市", width: "80px" },
        { prop: "hospitalName", label: "就诊医院", width: "80px" },
        { prop: "initiateProviderName", label: "下单服务商", width: "80px" },
        { prop: "creator", label: "创单操作员", width: "80px"},

        { prop: "cancelUser", label: "取消订单操作员", width: "80px" ,showIndexMap: [7,8]},
        { prop: "providerName", label: "接单服务商", width: "80px" },
        { prop: "transferProviderName", label: "转单服务商", width: "80px" },
        { prop: "channelName", label: "渠道链", width: "80px" },
        {
          prop: "backupImg",
          label: "补充内容",
          width: "80px",
          showIndexMap: [-1,1, 2,3],
          type: "imgMap",
        },
        {
          prop: "serviceTime",
          label: "陪诊时间",
          width: "80px",
          type: "createTime",
          realSortField: "startTime",
          sortable: true,
        },
        {
          prop: "payPrice",
          label: "服务费",
          width: "80px",
          showIndexMap: [-1,2,3, 4, 5, 6, 7,8],
          type: "payPrice",
        },
        {
          prop: "insurePrice",
          label: "保险费",
          width: "80px",
          showIndexMap: [-1,2,3, 4, 5, 6, 7,8],
          type: "insurePrice",
        },
        {
          prop: "allPrice",
          label: "总金额（服务费+保险费）",
          width: "80px",
          showIndexMap: [-1,2,3, 4, 5, 6, 7,8],
          type: "allPrice",
        },
        {
          prop: "mode",
          label: "派单模式",
          width: "80px",
          showIndexMap: [-1, 4, 5, 6, 7,8],
          type: "mode",
        },

        {
          prop: "receiveState",
          label: "接单状态",
          width: "80px",
          options: null,
          showIndexMap: [-1,4,8],
          type: "receiveState",
        },
        {
          prop: "employeeName",
          label: "陪诊师",
          width: "80px",
          showIndexMap: [-1,4, 5, 6, 7,8],
        },
        {
          prop: "comboPay",
          label: "支付方式",
          width: "80px",
          showIndexMap: [-1,3, 4, 5, 6,7,8],
          options: [
            { value: 0, label: "微信支付" },
            { value: 1, label: "套餐支付" },
          ],
        },
        {
          prop: "payTime",
          label: "支付时间",
          width: "80px",
          showIndexMap: [-1,3, 4, 5, 6,7,8],
        },
        {
          prop: "comboName",
          label: "套餐名称",
          width: "80px",
          showIndexMap: [-1,3, 4, 5, 6,7],
        },
        {
          prop: "userComboId",
          label: "套餐订单号",
          width: "80px",
          showIndexMap: [-1,3, 4, 5, 6,7,8],
        },
        {
          prop: 'policyNo',
          label: '保单号',
          width: "80px",
          showIndexMap: [-1,3, 4, 5, 6,7,8],
        },
        {
          prop: "insureStatus",
          label: "保单状态",
          width: "80px",
          type:'insureStatus',
          showIndexMap:[-1,3,4,5,6,7,8]
        },
        {
          prop: "commentState",
          label: "评价状态",
          width: "80px",
          options: null,
          showIndexMap: [-1,7],
          type: "commentState",
        },
        {
          prop: "star",
          label: "评价等级",
          width: "80px",
          options: null,
          showIndexMap: [-1,7],
        },
        {
          prop: "comment",
          label: "评价内容",
          width: "80px",
          options: null,
          showIndexMap: [-1,7],
        },
        {
          prop: "finishTime",
          label: "服务完成时间",
          width: "80px",
          options: null,
          showIndexMap: [-1,7],
        },
        {
          prop: "cancelReason",
          label: "取消原因",
          width: "80px",
          options: null,
          showIndexMap: [-1,8],
        },
        {
          prop: "refundProportion",
          type: "refundProportion",
          label: "退款比例",
          width: "80px",
          options: null,
          showIndexMap: [-1,8],
        },
        {
          prop: "refundAmount",
          label: "服务费退款金额",
          width: "120px",
          showIndexMap: [-1,7,8],
          type: "refundAmount",
        },
        {
          prop: "refundReason",
          label: "退款原因",
          width: "120px",
          showIndexMap: [-1,8],
          type: "refundReason",
        },
        {
          prop: "refundInsurePrice",
          label: "退款保险费",
          width: "80px",
          showIndexMap: [-1,8],
          type: "refundInsurePrice",
        },
        {
          prop: "transfer",
          label: "是否转单",
          width: "80px",
          options: [
            { value: 0, label: "否" },
            { value: 1, label: "是" },
          ],
          showIndexMap: [-1, 2,3,4,5,6,7,8],
        },
        { label: "收款类型", prop: "orderType", value: null,options:[{ value: 1, label: '线下收款码' },{ value:value=>value !== 1, label:'在线支付'}],showIndexMap:[2,3,4,5,6,7,8]},
        {
          prop: "createTime",
          label: "创建时间",
          width: "80px",
          sortable: true,
        },
        { prop: "20", label: "操作", width: "600px", type: "emits" ,showIndexMap: [1,2,3,4,5,6,7,8]},
      ],
      emitBtnMap: [
        {
          title: "查看",
          type: "",
          showIndexMap: [-1,0, 1, 2, 3, 4, 5, 6, 7],
          type: "check",
        },
        { title: "创建服务单", showIndexMap: [-1,0], type: "cheateOrder" },
        { title: "克隆服务单", showIndexMap: [-1,0,1], type: "cloneOrder" },
        { title: "订单二维码", showIndexMap: [-1,1], type: "orderCode" },
        { title: "更改订单", showIndexMap: [-1,1], type: "changeOrder" },
        {
          title: "更改预约",
          showIndexMap: [-1,2, 3, 4],
          type: "ChangingReservation",
        },
        { title: "派单", showIndexMap: [-1,2], type: "dispatcher" },
        { title: "转单", showIndexMap: [-1,0], type: "transfer" },
        { title: "重新派单", showIndexMap: [-1,3], type: "againDispatcher" },
        { title: "更换陪诊师", showIndexMap: [-1,4], type: "changeDesigners" },
        { title: "服务记录", showIndexMap: [-1,5, 6], type: "serviceRecord" },
        { title: "诊断报告", showIndexMap: [6], type: "diagnosisReport" },
        { title: "强制结束服务", showIndexMap: [-1,5], type: "closeService" },
        { title: "绑定拉卡拉订单", showIndexMap: [-1,1], type: "bindLKLOrder" ,providerParameterShowLogic:provider=>provider.offlineButton === 1},
        { title: "重新绑定拉卡拉订单", showIndexMap: [-1,2,3,4,5], type: "againBindLKLOrder",showLogic:row=>row.orderType === 1,providerParameterShowLogic:provider=>provider.offlineButton === 1 },
        {
          title: "取消订单",
          showIndexMap: [-1,0, 1, 2, 3, 4],
          type: "cancelOrder",
        },
        {
          title: "订单退款",
          showIndexMap: [-1, 6],
          type: "finishOrderRefund",
          showLogic: row => row.source === 2 && !row.combineOrderId, // 只在自营订单且非联合订单下显示
        },
      ],
      showTable: true,
      sortedTableData: [], // 确保初始化为数组
      sortProp: "createTime", // 添加默认排序字段
      sortOrder: "descending", // 添加默认排序方式
    };
  },
  computed: {
    // 合并本地配置与传入配置
    mergedColumns() {
      return this.titleList.map((col) => {
        const setting = this.columns.find((c) => c.prop === col.prop);
        return setting ? { ...col, hidden: setting.hidden } : col;
      });
    },

    // 过滤显示的列
    filteredColumns() {
      return this.mergedColumns.filter(
        (col) => !col.hidden || col.type === "emits" // 强制显示操作列
      );
    },

  },
  watch: {
    tabIndex() {
      this.showTable = false;
      requestAnimationFrame(() => {
        this.showTable = true;
      });
    },
    // 监听表格数据变化
    tableData: {
      immediate: true,
      handler(newVal) {
        this.sortedTableData = [...newVal];
        this.applySorting();
      },
    },
  },
  created() {
    this.applySorting();
  },
  mounted() {},
  methods: {
    getEmitBtnMap(row) {
      let orderState = +row.orderState - 1
      return this.emitBtnMap.filter(
        (e) => {
          if(e.showLogic && e.providerParameterShowLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.showLogic(row) && e.providerParameterShowLogic(this.currentProvider)
          }
          // 根据按钮本身特性决定是否显示
          if(e.showLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.showLogic(row)
          }
          // 根据服务商判断是否显示
          if(e.providerParameterShowLogic){
            return e.showIndexMap.indexOf(orderState) >= 0 && e.providerParameterShowLogic(this.currentProvider)
          }
          return e.showIndexMap.indexOf(orderState) >= 0
        }
      );
    },
    // 列显示判断
    shouldShowColumn(item) {
      if (item.prop === 'combineOrderId') return this.currentProvider.combineButton
      return (
        !item.hidden &&
        (!item.showIndexMap || item.showIndexMap.includes(+this.tabIndex))
      );
    },
    setFormData(id, key, value) {
      for (let i = 0; i < this.emitBtnMap.length; i++) {
        if (this.emitBtnMap[i].type === id) {
          this.$set(this.emitBtnMap[i], key, value);
          return;
        }
      }
    },
    triggerEmit(type, row) {
      console.log("emitBtnMap", this.getEmitBtnMap);
      this.$emit("editor", { type, row });
    },
    deleteRule(id) {
      console.log("row", id);
      this.$emit("delete", id);
    },
    getLabel(options, value) {
      return options.find((e) =>{
        if(typeof e.value === 'function')return e.value(value)
        return value == e.value
      })?.label;
    },
    handleSelectionChange(val) {
      this.$emit("select", val);
    },
    // 排序事件处理
    handleSortChange({ prop, order }) {
      if (order) {
        // 获取实际排序字段
        const columnConfig = this.titleList.find((col) => col.prop === prop);
        this.sortProp = columnConfig?.realSortField || prop;
        this.sortOrder = order;
      } else {
        // 重置为默认排序
        this.sortProp = "createTime";
        this.sortOrder = "descending";
      }
      this.applySorting();
    },
    // 执行排序逻辑
    applySorting() {
      // 获取实际排序字段
      const actualSortField =
        this.sortProp === "serviceTime" ? "startTime" : this.sortProp;
      this.sortedTableData = [...this.tableData].sort((a, b) => {
        // 处理默认排序
        if (!actualSortField || !this.sortOrder) {
          return new Date(b.createTime) - new Date(a.createTime);
        }
        // 统一日期解析方法
        const parseDate = (dateStr) => {
          try {
            // 处理时间范围格式
            const cleanStr = dateStr?.split("~")[0] || "";
            return new Date(
              cleanStr
                .replace(/[年月]/g, "-")
                .replace(/日/g, "")
                .trim()
            ).getTime();
          } catch {
            return 0;
          }
        };
        const aValue = parseDate(a[actualSortField]);
        const bValue = parseDate(b[actualSortField]);
        return this.sortOrder === "ascending"
          ? aValue - bValue
          : bValue - aValue;
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.buttonsBox {
  display: inline-block;
  margin-right: 10px;
}
.imgItem {
  width: 50px;
  height: 50px;
}
</style>
