<template>
  <el-drawer
    v-if="dialogVisible"
    :title="`${paramsData ? isClone ? '克隆' : '编辑' : '新增'}服务`"
    :visible.sync="dialogVisible"
    size="1200px"
    :before-close="handleClose"
    center
    append-to-body
  >
    <searchList
      :from-data="formList"
      :config="{ size: 24, labelWidth: '170px',groupId: 27000 }"
      @getImgUrlObj="getImgUrlObj"
    >
      <template slot="providerId" slot-scope="{ row }">
        <common-list-select
          v-model="row.value"
          :query-api="getAccompanyproviderPage"
          :page-size="1000"
          value-key="id"
          label-key="providerName"
          name="providerName"
          style="width: 70%;"
          placeholder="请选择"
          @change="handleProviderChange"
        >
        </common-list-select>
      </template>
      <template slot="classifyId" slot-scope="{ row }">
        <el-select
          v-model="row.value"
          placeholder="请选择"
          style="width: 70%;"
          :disabled="!selectedProviderId"
        >
          <el-option
            v-for="item in categoryOptions"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </template>
      <template slot="tag" slot-scope="{ row }">
        <tag-input
          v-model="row.value"
          :tags="tags"
          @update:tags="handleTagsUpdate"
        >
        </tag-input>
      </template>
      <template slot="content" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <div style="display: flex;" slot="icon" slot-scope="{ row }">
        <el-button @click="showIconMap = true" size="medium">点击选择图标和背景色</el-button>
        <img style="width: 36px;height: 36px;margin-left: 15px;" v-if="row.value" :src="imgServer + row.value" alt="" srcset="" />
        <iconMap :show="showIconMap" @close="showIconMap = false" @selectIcon="selectIcon"></iconMap>
      </div>
      <div style="display: flex;" slot="color" slot-scope="{ row }">
        <el-button @click="showIconMap = true" size="medium">点击选择图标和背景色</el-button>
        <div style="width: 36px;height: 36px;margin-left: 15px;" v-if="row.value" :style="{backgroundColor:row.value}" ></div>
      </div>
      <template slot="notice" slot-scope="{ row }">
        <formEditor v-model="row.value" />
      </template>
      <div slot="btnList" style="margin-top:50px;">
        <el-button type="primary" size="mini" @click="confirm">确定</el-button>
        <el-button type="primary" size="mini" @click="close">取消</el-button>
      </div>
    </searchList>
  </el-drawer>
</template>

<script>
  import {imgServer} from '@/api/config'

  import { getFromData } from "@/utils/index";
  import TagInput from '@/components/tagInput'
  import { serviceInsert,updateServiceData } from '@/api/dmCommunity/accompanyServiceManagement'
  import commonListSelect from '@/components/common-list-select.vue'
  import iconMap from './iconMap.vue'
  import {accompanyconfigQueryConfig,getAccompanyproviderPage,accompanyserviceclassifyQueryPage} from "@/api/dmCommunity/accompany-doctor";

  let defaultFormList = [
    { title: '服务名称', type: 1, id: 'serviceName', value: null, option: [], must: true, maxlength: 60, showWordLimit: true },
    { title: '价格', type: 12, id: 'price', value: null, option: [],must: true},
    { title: "所属服务商", id: "providerId", value: null, type: 20 },
    { title: "服务分类", id: "classifyId", value: null, type: 20, must: true },
    { title: '服务描述', type: 1, id: 'description', value: null, option: [], must: true, maxlength: 1000, showWordLimit: true },
    { title: '标签', type: 20, id: 'tag', value: null, option: [] },
    { title: '排序', type: 12, id: 'orderValue', value: null, option: [], },
    { title: '服务内容', type: 20, id: 'content', value: null, option: [],must: true, },
    { title: '预约须知', type: 20, id: 'notice', value: null, option: [],must: true, },
    { title: '列表图', type: 10, id: 'listImg', value: null, option: [],must: true,tip:'建议尺寸 160 * 160px' },
    { title: '详情图', type: 10, id: 'detailImg', value: null, option: [],must: true,tip:'建议尺寸 750 * 520px' },
    { title: '图标', type: 20, id: 'icon', value: null, option: [],must: true,tip:'请选择图标' },
    { title: '背景色', type: 20, id: 'color', value: null, option: [],must: true,tip:'请选择背景色' },
    { title: '启用状态', type: 8, id: 'state', value: 1,activeValue: 1, inactiveValue: 2, inactiveColor: "#C0CCDA", },
  ]
  export default {
    components:{
      TagInput,
      iconMap,
      commonListSelect
    },
    props:{
      show:{
        type:Boolean,
        default:false,
      },
      isClone:{
        type:Boolean,
        default:false
      },
      paramsData:{
        type:Object,
        default:()=>({})
      },
      tableData:{
        type:Array,
        default:()=>([])
      },
      setupdatecount:{
        type:Number,
        default:0
      },
    },
    data(){
      return{
        imgServer,
        dialogVisible:false,
        formList:JSON.parse(JSON.stringify(defaultFormList)),
        tags:[],
        showIconMap:false, // 显示iconMap
        inputs: [{value:''}], // 用一个数组来跟踪当前有多少个输入框
        getAccompanyproviderPage,
        categoryOptions: [],
        selectedProviderId: null
      }
    },
    watch:{
      show(n){
        this.dialogVisible = n
        if(!this.show){
          this.formList = JSON.parse(JSON.stringify(defaultFormList))
          this.tags = []
          this.inputs = [{value:''}]
          this.categoryOptions = []
          this.selectedProviderId = null
        }
      },
      setupdatecount(n){
        if(this.paramsData){
          console.log(this.paramsData,'this.paramsData----')
          // this.drugQueryOne()
          for(let key in this.paramsData){
            this.formList.forEach((item,index)=>{
              if(item.id == key){
                this.formList[index].value = this.paramsData[key]
              }
            })
          }
          if (this.paramsData.providerId) {
            this.selectedProviderId = this.paramsData.providerId
            this.getServiceCategories(this.paramsData.providerId)
          }
        }
      }
    },
    created(){},
    mounted(){},
    methods:{
      selectIcon({icon,color}){
        this.setFormData('icon', "value", icon);
        this.setFormData('color', "value", color);
      },
      deleteInput(index){
        if(this.inputs.length == 1) return
        this.inputs.splice(index, 1);
      },

      addInput() {
        this.inputs.push({ value: '' })
      },

      handleTagsUpdate(updatedTags){
        this.tags = updatedTags
      },

      handleProviderChange(value) {
        this.selectedProviderId = value
        // 清空服务分类的选择
        this.setFormData('classifyId', 'value', null)
        if (value) {
          this.getServiceCategories(value)
        } else {
          this.categoryOptions = []
        }
      },

      getServiceCategories(providerId) {
        accompanyserviceclassifyQueryPage({
          current: 1,
          size: 1000,
          condition: {
            providerId: providerId
          }
        }).then(res => {
          if (res.code == 0) {
            this.categoryOptions = res.data.records || []

            // 如果是编辑模式并且有分类ID
            if (this.paramsData && this.paramsData.classifyId) {
              const categoryExists = this.categoryOptions.some(item => item.id === this.paramsData.classifyId)
              if (categoryExists) {
                // 设置分类值
                this.setFormData('classifyId', 'value', this.paramsData.classifyId)
              }
            }
          } else {
            this.$message.error(res.msg || '获取服务分类失败')
            this.categoryOptions = []
          }
        }).catch(() => {
          this.categoryOptions = []
        })
      },

      async confirm(){
        let formParam = getFromData(this.formList)
        if(!formParam) return
        // 获取平台服务商id
        let {data:{defaultProviderId}} = await accompanyconfigQueryConfig({});
        formParam = {
          ...formParam,
          tag:Array.isArray(this.tags) ? this.tags.join(',') : this.tags,
          price:formParam.price * 100,
          state:formParam.state == 2 ? 0 : 1,
          source:1,
          providerId:formParam.providerId || defaultProviderId
        }
        console.log(formParam,'formParam-----')
        let res
        if(this.paramsData && !this.isClone){
          formParam.id = this.paramsData.id
          res = await updateServiceData(formParam)
        } else {
          res = await serviceInsert(formParam)
        }
        this.$eltool.successMsg(res.msg)
        this.close('query')
      },

      drugQueryOne(){
        drugQueryOne({ id: this.paramsData.id }).then(res=>{
          if(res.code == 0){
            this.formList.forEach((item, index) => {
              if(['tags'].includes(item.id)){
                // this.formList[index].value = res.data[item.id]
                this.tags = res.data[item.id] ? res.data[item.id].split(",") : []
              } else if(['price'].includes(item.id)){
                this.formList[index].value = res.data[item.id] / 100
              } else {
                this.formList[index].value = res.data[item.id]
              }
            })
            if (res.data.providerId) {
              this.selectedProviderId = res.data.providerId
              this.getServiceCategories(res.data.providerId)
            }
          }
        })
      },

      getImgUrlObj({ url, formData }) {
        this.setFormData(formData.id, "value", url);
      },

      setFormData(id, key, value) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            this.formList[i][key] = value;
            return;
          }
        }
      },

      getFormData(id, key) {
        for (let i = 0; i < this.formList.length; i++) {
          if (this.formList[i].id === id) {
            return this.formList[i][key];
          }
        }
      },

      handleClose() {
        this.$confirm('数据将不会保存，确认关闭？')
          .then(_ => {
            this.close()
          }).catch(_ => { })
      },
      close(type){
        this.$emit('close',type)
      }
    },
 }
</script>

<style lang='scss' scoped>
  ::v-deep .el-drawer__body {
    padding: 0 24px 24px;
  }
  .tags{
    display: flex;
    flex-direction: column;
    width: 60%;
  }
</style>
