<template>
  <div class="information-page-v7">
    <template v-for="page in pageContent">
      <div
        class="information-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="information-item">
          <div class="information-item-t">
            <div class="information-item-tl"></div>
            一、项目基础信息
          </div>
          <div class="information-item-c">
            <div class="information-item-ci">
              <img class="information-item-ci-i" :src="projectIco" alt="" />
              <span class="bold"> 项目名称： </span>
              {{ pageObject.title }}
            </div>
            <div class="information-item-ci">
              <img class="information-item-ci-i" :src="timeIco" alt="" />
              <span class="bold"> 执行周期： </span>
              {{ pageObject.startTime }} 至 {{ pageObject.endTime }}
            </div>
            <div class="information-item-ci">
              <img class="information-item-ci-i" :src="userProjectIco" alt="" />
              <span class="bold"> 项目方： </span>
              {{ pageObject.projectParty }}
            </div>
            <div class="information-item-ci">
              <img class="information-item-ci-i" :src="serviceIco" alt="" />
              <span class="bold"> 服务商： </span>
              {{ pageObject.serviceProvider }}
            </div>
          </div>
        </div>
        <div class="information-item">
          <div class="information-item-t">
            <div class="information-item-tl"></div>
            二、项目背景介绍
          </div>
          <div class="information-item-intro">
            {{ pageObject.background }}
          </div>
        </div>

        <pageBottomRectV2
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRectV2>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype", "filePrex"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      projectIco: this.domainUrl + this.filePrex + "icon-project.png",
      timeIco: this.domainUrl + this.filePrex + "icon-timer.png",
      userProjectIco: this.domainUrl + this.filePrex + "icon-project-user.png",
      serviceIco: this.domainUrl + this.filePrex + "icon-service.png",
      subTitle: "",
      pageContent: [
        {
          type: "information-page",
          uuid: "information-page_0",
          children: [],
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #e7effc;
$mainColorV2: #678ac8;
.information-page-v7 {
  background: #fff;
  .information-page {
    padding: 72px 20px 50px;
    box-sizing: border-box;
    position: relative;
  }
  .information-item {
    background: $mainColor;
    padding: 32px;
    border-radius: 27px;
    margin-bottom: 51px;
  }
  .information-item-t {
    font-weight: bold;
    font-size: 32px;
    color: $mainColorV2;
    display: flex;
    align-items: center;
  }
  .information-item-tl {
    width: 19px;
    height: 55px;
    background: $mainColorV2;
    border-radius: 51px;
    margin-right: 25px;
  }
  .information-item-c {
    padding-left: 44px;
    padding-top: 32px;
  }
  .information-item-ci {
    // display: flex;
    // align-items: center;
    margin-bottom: 9px;
    font-size: 19px;
    color: #333333;
    line-height: 2;
  }
  .information-item-ci-i {
    width: 32px;
    height: 32px;
    vertical-align: top;
    margin-right: 8px;
  }
  .information-item-ci-i-1 {
    width: 24px;
    height: 27px;
    vertical-align: top;
    margin-right: 8px;
  }
  .information-item-ci-i-2 {
    width: 24px;
    height: 24px;
    vertical-align: top;
    margin-right: 8px;
  }
  .information-item-ci-i-3 {
    width: 32px;
    height: 32px;
    vertical-align: top;
    margin-right: 8px;
  }
  .information-item-ci-i-4 {
    width: 32px;
    height: 32px;
    vertical-align: top;
    margin-right: 8px;
  }
  .bold {
    font-weight: 550;
  }
  .information-item-intro {
    font-weight: 400;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
    padding-top: 33px;
    padding-left: 44px;
    box-sizing: border-box;
  }
}
</style>