import reportPageNumber from '@/components/ui-report/components/report-page-number.vue'
import pageBottomRect from '@/components/ui-report/pharmacy-visit-analysis-report/components/page-bottom-rect.vue'
import pageBottomRectV2 from '@/components/ui-report/pharmacy-visit-check-report/components/page-bottom-rect-v2.vue'
export default {
  props: {
    pageNumObject: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  components: {
    reportPageNumber,
    pageBottomRect,
    pageBottomRectV2
  },
  data() {
    return {
      uuidCount: 100,
      uuidKey: 'default',
      waitIndex: 0,
      errorGapCount: 6,
      pageHeight: this.pageSize.height,
      spaceImageCount: 1,
      saTime: 1000
    }
  },
  methods: {
    updateWaiting() {
      this.waitIndex += 1;
    },
    getUuid() {
      this.uuidCount += 1;
      return this.uuidKey + "_" + this.uuidCount;
    },
    getRenderHeight(uuid) {
      let dom = document.getElementById(uuid);
      let offsetHeight = dom.clientHeight;
      let offsetTop = dom.offsetTop;
      return {
        offsetHeight,
        offsetTop,
      };
    },
    trimSuccess(params = {}) {
      console.log('trimSuccess')
      this.trimPagingInformation();
      this.$emit('success', params);
    },
    getTargetTime(str) {
      let a1 = str.split("-");
      return a1[0] + "年" + a1[1] + "月" + a1[2] + "日";
    },
    rendCurrent(item, pageObject = {}) {
      return new Promise((resolve, reject) => {
        let idx = this.waitIndex;
        if (!this.pageContent[idx]) {
          this.pageContent.push({
            ...pageObject,
            pageNum: this.waitIndex + 1,
            uuid: this.getUuid(),
            echatUuid: this.getUuid()
          })
        }
        let uuid = this.pageContent[idx].uuid;
        this.pageContent[idx].children.push(item);
        this.$nextTick(async () => {
          let { offsetHeight, offsetTop } = this.getRenderHeight(uuid);
          if (
            offsetHeight + offsetTop + this.errorGapCount >
            this.pageHeight
          ) {
            let len = this.pageContent[idx].children.length;
            this.pageContent[idx].children.splice(len - 1, 1);
            this.updateWaiting();
            await this.rendCurrent(item, pageObject);
          } else if (
            offsetHeight + offsetTop + this.errorGapCount >
            this.pageHeight
          ) {
            this.updateWaiting();
          }
          resolve(true);
        });
      })
    },
    sceentInit(pageObject = {}, imageList = []) {
      if (!pageObject.type) {
        return;
      }
      let spaceCount = this.spaceImageCount;
      console.log('this.imageList', this.imageList)
      if (imageList.length > spaceCount) {
        let count = imageList.length - spaceCount;
        let idx = this.pageContent.findIndex(
          (item) => item.type === pageObject.type
        );
        console.log('count', count)
        let tidx = 0;
        while (count > 0) {
          count -= spaceCount;
          tidx += 1;
          idx += 1;
          this.pageContent.splice(idx, 0, {
            ...pageObject,
            uuid: this.getUuid(),
            end: spaceCount * (tidx + 1),
            start: spaceCount * tidx,
            echatUuid: this.getUuid(),
          });
        }
      }
    },
    trimPagingInformation() {
      let pageingObject = {};
      if(Array.isArray(this.pageContent) && this.pageContent.length !== 0) {
        this.pageContent.forEach((item,index) => {
          pageingObject[item.uuid] = index + 1;
        })
      } else if(this.pageUuid){
        pageingObject[this.pageUuid] = 1;
      }
      // console.log('pageingObject====123',pageingObject,this.pageObject.parentUuid,this.pageObject)
      this.$emit('updatePageing',{
        pageingObject,
        parentUuid: this.pageObject.parentUuid
      })
      // console.log('pageingObject==',pageingObject)
    }
  }
}