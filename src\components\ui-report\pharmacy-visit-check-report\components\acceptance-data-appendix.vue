<template>
  <div class="acceptance-data-appendix-page-v7">
    <template v-for="page in pageContent">
      <div
        class="acceptance-data-appendix-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="acceptance-data-appendix-t" v-if="page.once">
          <div class="acceptance-data-appendix-tl"></div>
          五、验收数据附表
        </div>
        <div class="acceptance-data-appendix-c">
          <div class="acceptance-data-appendix-tip" v-if="page.once">
            任务数量合计：{{ pageObject.taskNum }} 项目执行人数：{{
              pageObject.taskUserNum
            }}
            人 合格任务量合计：{{ pageObject.passNum }} 不合格任务量合计：{{
              pageObject.noPassNum
            }}
            验收合格率：{{ pageObject.passRateStr }}
          </div>
          <div class="pull-table-box" :id="page.uuid">
            <div class="pull-table">
              <el-table
                :data="page.children"
                style="width: 100%"
                header-row-class-name="pull-new-details-table-row"
                cell-class-name="pull-new-details-table-cell"
              >
                <template v-for="col in tableHeader">
                  <el-table-column
                    :prop="col.prop"
                    :label="col.label"
                    :width="col.width"
                    align="center"
                    :key="col.prop"
                    :isReportTable="true"
                  >
                  </el-table-column>
                </template>
              </el-table>
            </div>
          </div>
        </div>

        <pageBottomRectV2
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRectV2>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype", "filePrex"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      uuidKey: "acceptance-data-appendix",
      projectIco: this.domainUrl + this.filePrex + "icon-project.png",
      timeIco: this.domainUrl + this.filePrex + "icon-timer.png",
      userProjectIco: this.domainUrl + this.filePrex + "icon-project-user.png",
      serviceIco: this.domainUrl + this.filePrex + "icon-service.png",
      subTitle: "",
      pageContent: [
        {
          type: "acceptance-data-appendix-page",
          uuid: "acceptance-data-appendix-page_0",
          children: [],
          once: true,
        },
      ],
      tableHeader: [
        {
          label: "执行人名称",
          prop: "userName",
          width: 131,
        },
        {
          label: "手机号",
          prop: "userPhone",
          width: 145,
        },
        {
          label: "合格量",
          prop: "passNum",
          width: 109,
        },
        {
          label: "不合格率",
          prop: "noPassRateText",
          width: 109,
        },
        {
          label: "合格率",
          prop: "passRateText",
          width: 109,
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initRender(list = []) {
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        await this.rendCurrent(item, {
          type: "acceptance-data-appendix-page",
          once: this.pageContent.length === 0,
          children: [],
        });
      }
      await this.$nextTick();
      this.trimSuccess();
    },
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle, userList } = pageObject;
      this.pageHeight = this.pageSize.height - 63;
      this.subTitle = subTitle;
      this.initRender(userList);
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #e7effc;
$mainColorV2: #678ac8;
.acceptance-data-appendix-page-v7 {
  background: #fff;
  font-family: Microsoft YaHei, Microsoft YaHei;
  .acceptance-data-appendix-page {
    position: relative;
    padding-top: 72px;
    padding-bottom: 50px;
  }
  .acceptance-data-appendix-t {
    font-weight: bold;
    font-size: 32px;
    color: $mainColorV2;
    display: flex;
    align-items: center;
  }
  .acceptance-data-appendix-tl {
    margin-right: 25px;
    width: 19px;
    height: 55px;
    background: $mainColorV2;
  }
  .acceptance-data-appendix-c {
    padding-top: 32px;
    padding-left: 63px;
    padding-right: 62px;
  }
  .acceptance-data-appendix-tip {
    font-weight: 400;
    font-size: 19px;
    color: #333333;
    line-height: 28px;
    margin-bottom: 16px;
  }
  .pull-table-box {
    padding: 0 32px;
  }
}
</style>