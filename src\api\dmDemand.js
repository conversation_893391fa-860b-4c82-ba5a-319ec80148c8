/* eslint-disable */
import requestV1 from '@/common/utils/modules/request'

const prefix = '/dm/api/v1'

/**
 * 需求管理
 */

// 根据ids批量删除
export function deleteBatch (data) {
    return requestV1.deleteForm(`${prefix}/demand/delete/batch/${data.ids}`);
}

// 保存数据
export function insert (data) {
    return requestV1.postJson(`${prefix}/demand/insert`, data)
}

// 列表查询
export function queryList (data) {
    return requestV1.get(`${prefix}/demand/query/list`, data)
}

// 根据id查询单个
export function queryOne (data) {
    return requestV1.get(`${prefix}/demand/query/one`, data)
}

// 分页列表查询
export function queryPage (data) {
    return requestV1.postJson(`${prefix}/demand/query/page`, data)
}

// 查询结案数据展示
export function queryDemandReviewById (data) {
    return requestV1.get(`${prefix}/demand/queryDemandReviewById`, data)
}

// 更新数据
export function update (data) {
    return requestV1.putJson(`${prefix}/demand/update`, data)
}

// 开始完成取消操作
export function updateStatus (data) {
    return requestV1.putJson(`${prefix}/demand/updateStatus`, data)
}

// 上传结案
export function completedFinalReport (data) {
    return requestV1.putJson(`${prefix}/demand/completedFinalReport`, data)
}

// 查询任务列表
export function queryTaskProcessByDemanId (data) {
    return requestV1.get(`${prefix}/demand/queryTaskProcessByDemanId`, data)
}

// 获取关联 帖子列表  /dm/api/v1/demand/queryPostMessageByDemanId
export function getQueryPostMessageByDemanId(data){
  return requestV1.get(`${prefix}/demand/queryPostMessageByDemanId`, data)
}

// 批量生成短链接 /dm/api/v1/postmessage/generate/snapshop/link
export function generatesnapshoplink(data){
  return requestV1.postJson(`${prefix}/postmessage/generate/snapshop/link`, data)
}

// 一键同步项目名
export function syncDemandNames (data) {
  return requestV1.postJson(`${prefix}/demand/sync/demandNames`, data)
}

// 导出项目服务报告-精准地推
export function exportProjectAccurateId (data) {
  return requestV1.get(`${prefix}/demand/get/report/precise`, data)
}

// 导出项目服务报告-普通地推-结构数据
export function exportProjectQueryOne (data) {
  return requestV1.get(`${prefix}/demand/get/report/ordinary`, data)
}

// 导出项目服务报告-普通地推-列表数据
export function exportProjectCommonList (data) {
  return requestV1.postJson(`${prefix}/minichannellinklog/query/page`, data)
}

// 任务报表-普通报表
export function todotasksGetReportOrdinary (data,headers = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/ordinary`, data,null,headers)
}

// 任务报表-精准报表
export function todotasksGetReportPrecise (data,headers = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/precise`, data,null,headers)
}

// 任务报表-精准报告
export function todotasksGetReportPreciseDetailSubmitLog (data,headers = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/precise/detail/submitLog`, data,null,headers)
}

export function todotasksGetReportPreciseDetailFrom (data,headers = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/precise/detail/from`, data,null,headers)
}

// 获取报告下的表单数据
export function todotasksGetFromData (data) {
  return requestV1.get(`${prefix}/todotasks/get/form/data`, data)
}

// 修改报告里的比例 
export function todotasksUpdateProportionOffice (data) {
  return requestV1.postJson(`${prefix}/todotasks/update/proportion/office`, data)
}

// 报告数据统计 
export function todotasksExportReportData(params,fileName='数据统计.xlsx') {
  return requestV1.download(`${prefix}/todotasks/export/report/data`, params,fileName,'post',true)
}

// 导出报告月度普通地推类型文件
export function todotasksExportReportDataOrdinary(params,fileName='数据统计.xlsx') {
  return requestV1.download(`${prefix}/todotasks/export/report/data/ordinary`, params,fileName,'post',true)
}

// 线下线上用户活动项目报告
export function todotasksGetReportDetail(data,headers = {}) {
  return requestV1.get(`${prefix}/todotasks/get/report/detail`, data, null, headers)
}

// 用户活动项目报告 
export function todotasksExportReportDataUser(data,fileName='数据统计.xlsx') {
  return requestV1.download(`${prefix}/todotasks/export/report/data/user/activity`, data,fileName,'post',true)
}

// 用户活动圈子统计 
export function useractivityinvitelogCircleStaticstics(data = {},headers = {}) {
  return requestV1.postJson(`${prefix}/useractivityinvitelog/circle/statistics`, data, null, headers)
}

// 科普报告圈子分布
export function useractivityinvitelogCircleDistributeStaticstics(data = {},headers = {}) {
  return requestV1.postJson(`${prefix}/useractivityinvitelog/circle/distribute/statistics`, data, null, headers)
}

// 科普笔记数据导出 
export function todotasksExportReportDataNodes(data,fileName='数据统计.xlsx') {
  return requestV1.download(`${prefix}/todotasks/export/report/data/notes`, data,fileName,'post',true)
}