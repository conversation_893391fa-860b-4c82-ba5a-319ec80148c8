<template>
  <div class="dynamics-table-v2">
    <template v-for="page in pageContent">
      <div
        class="dynamics-table-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="topic-rect-top"></div>
        <template
          v-if="page.once && !['visit-date-page-table','visit-duration-page-table'].includes(inType)"
        >
          <div class="topic-rect-ct">
            <div class="topic-rect-ctl"></div>
            拜访日期
          </div>
        </template>
        <div class="dynamics-table-table-box table-size14" :id="page.uuid">
          <div
            class="pull-table"
            :style="{
              '--zoom': widthZoom,
              '--borderWidth': borderWidth,
              '--width': tableWidth + 'px',
            }"
          >
            <el-table
              :data="page.children"
              header-row-class-name="pull-new-details-table-row"
              cell-class-name="pull-new-details-table-cell"
              style="width: 100%"
            >
              <template v-for="col in tableHeader">
                <el-table-column
                  :prop="col.prop"
                  :label="col.label"
                  :width="col.width"
                  align="center"
                  :key="col.prop"
                  :isReportTable="true"
                >
                </el-table-column>
              </template>
            </el-table>
          </div>
        </div>
        <reportPageNumber
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></reportPageNumber>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {};
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
    inType: {
      type: String,
      default: "dynamicsTable",
    },
  },
  data() {
    return {
      borderWidth: null,
      pageContent: [],
      uuidKey: "dynamics-table-page",
      widthZoom: 1,
      uuidCount: 100,
      tableWidth: this.pageSize.width - 110,
      boxWidth: this.pageSize.width - 110,
      bgUrl:
        this.domainUrl +
        "image/business/ui-report-image/earth-push-project-report/styleOne/icon-information-top-bg.png",
    };
  },
  methods: {
    calculateZoomFactor(zoom) {
      return (1 / zoom).toFixed(3);
    },
    async initTableHeader() {
      let sw = this.boxWidth;
      let widthZoom = 1;
      let headersWidth = 0;
      this.tableHeader.forEach((item) => {
        headersWidth += item.width;
      });
      if (headersWidth < sw) {
        headersWidth = sw;
        widthZoom = 1;
        this.tableHeader[this.tableHeader.length - 1].width = null;
      } else {
        headersWidth += this.tableHeader.length * 2;
        this.tableHeader[this.tableHeader.length - 1].width = null;
        widthZoom = (sw / headersWidth).toFixed(3);
      }
      this.widthZoom = widthZoom;
      this.tableWidth = headersWidth;
      this.borderWidth =
        Math.ceil(this.calculateZoomFactor(widthZoom) * 1) + "px";
      await this.$nextTick();
      this.initRender();
    },
    getEnumText(value, list) {
      const itemType = list.find((item) => item.value === value);
      return itemType && Object.keys(itemType).length ? itemType.label : "";
    },
    async initRender() {
      this.pageContent.push({
        type: "dynamics-table-page",
        children: [],
        once: true,
        uuid: this.getUuid(),
      });
      let pageObject = this.pageObject || {};
      let itemV = pageObject.answerReportVo || [];
      let list = itemV.formTemplateOptions;
      console.log("list", list);
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        await this.rendCurrent(item, {
          type: "dynamics-table-page",
          once: false,
          children: [],
        });
      }
      this.trimSuccess({});
    },
  },
  watch: {
    updatecount(n) {
      this.pageHeight = this.pageSize.height - 63;
      this.uuidKey = this.inType;
      let pageObject = this.pageObject || {};
      this.initTableHeader();
    },
  },
  computed: {
    tableHeader(n) {
      let pageObject = this.pageObject || {};
      n = []
      if (this.inType === "visit-date-page-table") {
        n = [
          {
            prop: "optionValue",
            label: "拜访日期",
            width: 220,
          },
          {
            prop: "selectOptionNum",
            label: "人数",
            width: 220,
          },
          {
            prop: "selectOptionProportion",
            label: "占比",
            width: 220,
          },
        ];
      } else if(this.inType === 'visit-duration-page-table') {
        n = [
          {
            prop: "optionValue",
            label: "拜访时长（分钟）",
            width: 220,
          },
          {
            prop: "selectOptionNum",
            label: "人数",
            width: 220,
          },
          {
            prop: "selectOptionProportion",
            label: "占比",
            width: 220,
          },
        ]
      }
      return n;
    },
  },
};
</script>

<style lang='scss' scoped>
$mainColor: #609bd3;
.dynamics-table-v2 {
  .dynamics-table-page {
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    padding: 165px 40px 20px;
  }
  .topic-rect-top {
    height: 103px;
    width: 100%;
    background: var(--backgroud-top-bg);
    position: absolute;
    top: 0;
    left: 0;
  }
  .topic-rect-ct {
    font-weight: bold;
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    margin-bottom: 33px;
  }
  .topic-rect-ctl {
    width: 12px;
    height: 49px;
    border-radius: 20px;
    margin-right: 13px;
    background: $mainColor;
  }
  .topic-echart {
    width: 100%;
    height: 816px;
  }
  .echart-item {
    width: 100%;
    height: 100%;
  }
  .dynamics-table-table-box {
    display: flex;
    justify-content: center;
  }
  .pull-table {
    zoom: var(--zoom, 1);
    width: var(--width);
  }
}
</style>