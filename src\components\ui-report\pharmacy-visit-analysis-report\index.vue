<template>
  <div
    class="pageContent yaHei-family"
    v-loading="pageLoading"
    :style="{
      '--backgroud-top-bg': 'url(' + domainUrl + bgImg + ')',
      '--backgroud-top-bg-v2': 'url(' + domainUrl + bgImgV2 + ')',
      '--backgroud-top-bg-v3': 'url(' + domainUrl + bgImgV3 + ')',
      '--title-bg': 'url(' + domainUrl + titleBgImg + ')',
      '--border-tb': 'url(' + domainUrl + borderTb + ')',
      '--border-lr': 'url(' + domainUrl + borderLr + ')',
      '--border-tb-v2': 'url(' + domainUrl + borderTbV2 + ')',
      '--border-bb-v2': 'url(' + domainUrl + borderBbV2 + ')',
      '--border-lr-v2': 'url(' + domainUrl + borderLrV2 + ')',
    }"
  >
    <template v-for="(page, index) in pageContent">
      <div
        class="everyPage"
        :key="index"
        :style="{
          width: pageSize.width + 'px',
          height: pageSize.height + 'px',
        }"
        v-if="!page.authHeight"
      >
        <template v-if="page.type === 'thumb'">
          <thumb
            :pageObject="page.pageObject"
            :updatecount="thumbUpdateCount"
            @success="updateSuccess"
            :showPaging="false"
          >
          </thumb>
        </template>
        <template v-if="page.type === 'directory'">
          <directory
            :pageObject="page.pageObject"
            :updatecount="directoryUpdateCount"
            :moduleRangeObject="moduleRangeObject"
            @success="updateSuccess"
          ></directory>
        </template>
      </div>
      <template v-else-if="page.authHeight">
        <template v-if="page.type === 'background'">
          <background
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="backgroundUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></background>
        </template>
        <template v-else-if="page.type === 'project-execution-process'">
          <projectExecutionProcess
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="projectExecutionProcessUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></projectExecutionProcess>
        </template>
        <template v-else-if="page.type === 'information'">
          <information
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="informationUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></information>
        </template>
        <template v-else-if="page.type === 'execution-personnel-data'">
          <execution-personnel-data
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="executionPersonnelDataUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="executionPersonnelDataRenderUpdateCount"
          ></execution-personnel-data>
        </template>
        <template v-else-if="page.type === 'dynamics-form'">
          <dynamics-form
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="dynamicsFormUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></dynamics-form>
        </template>
        <template
          v-else-if="
            ['customer-feedback-page', 'keyword-description-page'].includes(
              page.type
            )
          "
        >
          <topic-rect-v2
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="keywordDescriptionUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="keywordDescriptionRenderUpdateCount"
          ></topic-rect-v2>
        </template>
        <template v-else-if="page.type === 'visit-duration-page'">
          <topic-rect-v5
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="visitDurationUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="visitDurationRenderUpdateCount"
          ></topic-rect-v5>
        </template>
        <template v-else-if="page.type === 'visit-date-page'">
          <topic-rect-v5
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="visitDateUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="visitDateRenderUpdateCount"
          ></topic-rect-v5>
        </template>
        <template
          v-else-if="
            [
              'service-company-description',
              'research-conclusion-page',
            ].includes(page.type)
          "
        >
          <research-conclusion
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="descUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></research-conclusion>
        </template>
        <template v-else-if="page.type === 'distribution-of-pharmacies'">
          <topic-rect-v3
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="distributionOfUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="distributionOfRenderUpdateCount"
          ></topic-rect-v3>
        </template>
        <template v-else-if="page.type === 'execution-details-page'">
          <executionDetails
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="executionDetailUpdateCount"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></executionDetails>
        </template>
        <template v-else-if="page.type === 'number-of-visits-to-pharmacies'">
          <topic-rect-v4
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="numberOfVisitUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
            :renderUpdateCount="numberOfVisitRenderUpdateCount"
            :isEchartOpen="false"
          ></topic-rect-v4>
        </template>
        <template v-else-if="page.type === 'visit-date-page-table'">
          <dynamics-table
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="visitDataPageUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></dynamics-table>
        </template>
        <template v-else-if="page.type === 'visit-duration-page-table'">
          <dynamics-table
            :key="index"
            :pageObject="page.pageObject"
            :updatecount="visitDataPageUpdateCount"
            :inType="page.type"
            @success="updateSuccess"
            @updatePageing="updatePageing"
            :pageNumObject="pageNumObject"
          ></dynamics-table>
        </template>
      </template>
    </template>
  </div>
</template>

<script>
import commonMixin from "@/components/ui-report/mixins/common.js";
import { getQueryStr, domainURL } from "@/utils/index";
import { imgServer } from "@/api/config";
import thumb from "./components/thumb.vue";
import directory from "./components/directory.vue";
import background from "./components/background.vue";
import projectExecutionProcess from "./components/project-execution-process.vue";
import information from "./components/information.vue";
import executionPersonnelData from "./components/execution-personnel-data.vue";
import dynamicsForm from "./components/dynamics-form.vue";
import topicRect from "@/components/ui-report/components/topic-rect.vue";
import topicRectV2 from "@/components/ui-report/components/topic-rect-v2.vue";
import topicRectV3 from "@/components/ui-report/components/topic-rect-v3.vue";
import topicRectV4 from "@/components/ui-report/components/topic-rect-v4.vue";
import topicRectV5 from "@/components/ui-report/components/topic-rect-v5.vue";
import dynamicsTable from "./components/dynamics-table.vue";
import researchConclusion from "./components/research-conclusion.vue";
import { loadScript } from "@/utils/index";
import { getVisitingPlanAnalysisReport } from "@/api/dm/visiting/visitingplan";
import executionDetails from "./components/execution-details-page.vue";
import { format } from "@/utils/index";
import { getGender } from "@/utils/enumeration.js";

export default {
  components: {
    thumb,
    directory,
    background,
    projectExecutionProcess,
    information,
    executionPersonnelData,
    dynamicsForm,
    topicRect,
    topicRectV2,
    researchConclusion,
    topicRectV3,
    executionDetails,
    topicRectV4,
    topicRectV5,
    dynamicsTable,
  },
  mixins: [commonMixin],
  provide() {
    return {
      pageSize: this.pageSize,
      domainUrl: this.domainUrl,
      pagetype: Number(this.pagetype),
      pageTopBgUrl: "",
      reportStyle: this.reportStyle,
    };
  },
  props: {
    businessId: {
      type: [Number, String],
      default: null,
    },
    pagetype: {
      type: [Number, String],
      default: "10",
    },
    reportStyle: {
      type: [Number, String],
      default: 1,
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      // domainUrl: "http://192.168.3.54:62917/",
      titleBgImg:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-title-bg.png",
      bgImg:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg.png",
      bgImgV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg-v2.png",
      bgImgV3:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-page-top-bg-v3.png",
      borderTb:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-tb.png",
      borderLr:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-lr.png",
      borderTbV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-tb-v3.png",
      borderBbV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-bb-v3.png",
      borderLrV2:
        "image/business/ui-report-image/pharmacy-visit-analysis-report/styleOne/icon-border-lr-v3.png",
      domainUrl: imgServer + "static/",
      domainUrlV2: "https://file.greenboniot.cn/",
      pageLoading: false,
      pageContent: [
        {
          text: "封面",
          type: "thumb",
          moduleKey: "thumb",
          pageObject: {},
        },
        {
          text: "目录",
          type: "directory",
          moduleKey: "directory",
          pageObject: {},
        },
        {
          text: "背景",
          type: "background",
          moduleKey: "background",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行流程",
          type: "project-execution-process",
          moduleKey: "project-execution-process",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目信息",
          type: "information",
          moduleKey: "information",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行人数据统计",
          type: "execution-personnel-data",
          moduleKey: "execution-personnel-data",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行人拜访药店数量统计",
          type: "number-of-visits-to-pharmacies",
          moduleKey: "execution-personnel-data",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "项目执行人数据统计",
          type: "execution-details-page",
          moduleKey: "execution-personnel-data",
          pageObject: {},
          authHeight: true,
          // updatecount
        },
        {
          text: "动态表单",
          type: "dynamics-form",
          moduleKey: "dynamics-form",
          pageObject: {},
          authHeight: true,
        },
        // {
        //   text: "药店分布",
        //   type: "distribution-of-pharmacies",
        //   moduleKey: "formContent",
        //   pageObject: {},
        //   authHeight: true,
        // },
        {
          text: "拜访日期",
          type: "visit-date-page",
          moduleKey: "formContent",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "拜访日期",
          type: "visit-date-page-table",
          moduleKey: "formContent",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "拜访时长",
          type: "visit-duration-page",
          moduleKey: "formContent",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "拜访时长",
          type: "visit-duration-page-table",
          moduleKey: "formContent",
          pageObject: {},
          authHeight: true,
        },
        {
          text: "传递关联信息统计",
          type: "keyword-description-page",
          pageObject: {},
          moduleKey: "formContent",
          authHeight: true,
        },
        {
          text: "客户回馈统计",
          type: "customer-feedback-page",
          pageObject: {},
          moduleKey: "formContent",
          authHeight: true,
        },
        {
          text: "调研结论或建议",
          type: "research-conclusion-page",
          pageObject: {},
          moduleKey: "formContent",
          authHeight: true,
        },
        {
          text: "服务公司说明",
          type: "service-company-description",
          pageObject: {},
          moduleKey: "formContent",
          authHeight: true,
        },
        //
      ],
      targetCount: 18,
      fixedFieldObject: {
        projectParty: "广州绿葆网络发展有限公司", // 项目方---绿葆自己
        productTaskNumber: 0, // 地推团队数量
      },
      thumbUpdateCount: 0,
      saTime: null,
      directoryUpdateCount: 0,
      uuidCountKey: 1000,
      topicRectUpdateCount: 0,
      backgroundUpdateCount: 0,
      projectExecutionProcessUpdateCount: 0,
      informationUpdateCount: 0,
      executionPersonnelDataUpdateCount: 0,
      dynamicsFormUpdateCount: 0,
      keywordDescriptionUpdateCount: 0,
      visitDurationUpdateCount: 0,
      visitDateUpdateCount: 0,
      descUpdateCount: 0,
      executionPersonnelDataRenderUpdateCount: 0,
      visitDurationRenderUpdateCount: 0,
      visitDateRenderUpdateCount: 0,
      keywordDescriptionRenderUpdateCount: 0,
      distributionOfUpdateCount: 0,
      distributionOfRenderUpdateCount: 0,
      executionDetailUpdateCount: 0,
      numberOfVisitUpdateCount: 0,
      numberOfVisitRenderUpdateCount: 0,
      visitDataPageUpdateCount: 0,
    };
  },
  methods: {
    stripHtmlTags(html = "") {
      return html.replace(/<[^>]*>|&nbsp;/g, "");
    },
    getUuid() {
      this.uuidCountKey += 1;
      return this.uuidCountKey;
    },
    async exportProjectAccurateId() {
      const res = await getVisitingPlanAnalysisReport(
        {
          id: this.businessId,
        },
        {
          "no-time-manage": 1,
        }
      );
      const data = res.data;
      let commonObject = {
        title: "",
        ctitle: "",
        projectParty: "江西施美药业股份有限公司",
        serviceProvider: "广州兰图科技有限公司",
        pharmacyNumber: 0,
        taskUserNum: 0,
        monthText: "",
        createTimeText: "",
        visitCustomer: "",
        visitTerminal: "",
        demandLocation: "",
        demandDescribe: "",
        // monthText: format()
      };
      let backgroundObject = {
        content: "",
      };
      let researchConclusionObject = {
        content: "",
      };
      let pharmaciesObject = {
        planObjectListList: [],
      };
      let drugPlanList = [];
      let ctype = 3;
      if (data.visitingPlan instanceof Object) {
        commonObject.title = data.visitingPlan.title;
        commonObject.ctitle = data.visitingPlan.title;
        if (commonObject.title.includes("在")) {
          let arr = commonObject.title.split("在");
          commonObject.title = arr[0] + "在<br />" + arr[1];
        }
        commonObject.createTimeText = data.visitingPlan.reportTimeStr;
        backgroundObject.content = data.visitingPlan.background;
        commonObject.visitTerminal = this.stripHtmlTags(
          data.visitingPlan.visitingTerminals
        );
        commonObject.visitCustomer = this.stripHtmlTags(
          data.visitingPlan.visitingCustom
        );
        researchConclusionObject.content = data.visitingPlan.visitingSummarize;
        ctype = data.visitingPlan.type;
        commonObject.demandLocation = data.visitingPlan.demandLocation;
        commonObject.demandDescribe = data.visitingPlan.demandDescribe;
      }
      if (data.demand instanceof Object) {
        let createTime = data.demand.createTime;
        commonObject.monthText = format(createTime, "YYYY-MM");
        commonObject.taskUserNum = data.demand.taskUserNum;
      }
      // 药店分布
      if (data.planObjectListList instanceof Object) {
        commonObject.pharmacyNumber = data.planObjectListList.length;
      }
      if (data.drugPlanList instanceof Object) {
        drugPlanList = data.drugPlanList;
      }
      // 执行人信息
      let userReportVo = {};
      if (data.userReportVo instanceof Object) {
        userReportVo = data.userReportVo || {};
      }
      let visitDurationReportVoList = [];
      let visitTimeReportVoList = [];
      if (data.visitDurationReportVoList instanceof Object) {
        visitDurationReportVoList = data.visitDurationReportVoList;
      }
      // 拜访日期
      if (data.visitTimeReportVoList instanceof Object) {
        visitTimeReportVoList = data.visitTimeReportVoList;
      }
      let idCount = 1000;
      const answerReportVoList = [];
      answerReportVoList.push({
        title: "拜访日期",
        type: 3,
        id: idCount,
        belong: "visitDate",
        placeholder: "日期 YYYY-MM-DD HH:mm:ss",
        formTemplateOptions: visitTimeReportVoList.map((item) => {
          return {
            ...item,
            optionValue: item.descValue,
            selectOptionNum: item.count,
            selectOptionProportion: item.percentage + "%",
          };
        }),
      });
      idCount += 1;
      answerReportVoList.push({
        title: "拜访时长",
        type: 3,
        id: idCount,
        belong: "visitDuration",
        placeholder: "请输入",
        formTemplateOptions: visitDurationReportVoList.map((item) => {
          return {
            ...item,
            label: item.descValue,
            value: item.descValue,
            uuid: this.getUuid(),
            optionValue: item.descValue,
            selectOptionNum: item.count,
            selectOptionProportion: item.percentage + "%",
          };
        }),
      });
      idCount += 1;

      if (data.visitTransmitReportVoList instanceof Object) {
        let submitCount = 0;
        for (let k = 0; k < data.visitTransmitReportVoList.length; k++) {
          submitCount += data.visitTransmitReportVoList[k].count - 0;
        }
        console.log(
          "data.visitTransmitReportVoList===",
          data.visitTransmitReportVoList
        );
        answerReportVoList.push({
          title: "传递关联信息统计",
          type: 2,
          belong: "transferRelatedInformations",
          placeholder: "",
          id: idCount,
          // 实际提交: xx份数
          submitCount: submitCount,
          formTemplateOptions: data.visitTransmitReportVoList.map((item) => {
            return {
              ...item,
              label: item.descValue,
              value: item.descValue,
              uuid: this.getUuid(),
              optionValue: item.descValue,
              selectOptionNum: item.count,
              selectOptionProportion: item.percentage + "%",
            };
          }),
        });
        idCount += 1;
      }

      if (data.visitFeedbackReportVoList instanceof Object) {
        let submitCount = 0;
        for (let k = 0; k < data.visitTransmitReportVoList.length; k++) {
          submitCount += data.visitTransmitReportVoList[k].count - 0;
        }
        answerReportVoList.push({
          title: "客户回馈",
          type: 2,
          belong: "customerFeedback",
          placeholder: "",
          id: idCount,
          submitCount: submitCount,
          formTemplateOptions: data.visitFeedbackReportVoList.map((item) => {
            return {
              ...item,
              label: item.descValue,
              value: item.descValue,
              uuid: this.getUuid(),
              optionValue: item.descValue,
              selectOptionNum: item.count,
              selectOptionProportion: item.percentage + "%",
            };
          }),
        });
        idCount += 1;
      }
      answerReportVoList.push({
        title: "终端位置",
        type: 3,
        belong: "terminalLocation",
        placeholder: "请输入",
        id: idCount,
      });
      console.log("commonObject========", commonObject);
      let genderResult = getGender();

      this.pageContent.forEach((item) => {
        switch (item.type) {
          case "thumb":
            item.pageObject = {
              parentUuid: item.type,
              ...commonObject,
            };
            break;
          case "directory":
            item.pageObject = {
              parentUuid: item.type,
            };
            break;
          case "background":
            item.pageObject = {
              subTitle: "一、项目背景",
              parentUuid: item.type,
              ...backgroundObject,
            };
            break;
          case "dynamics-form":
            item.pageObject = {
              businessId: this.businessId,
              parentUuid: item.type,
              answerReportVoList,
              subTitle: "五、相关调研模块",
            };
            break;
          case "execution-personnel-data":
            item.pageObject = {
              userReportVo,
              parentUuid: item.type,
              subTitle: "四、项目执行人数据统计",
              planId: this.businessId,
              planType: ctype,
            };
            break;
          case "project-execution-process":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "二、项目执行流程",
            };
            break;
          case "information":
            item.pageObject = {
              parentUuid: item.type,
              subTitle: "三、项目总览",
              ...commonObject,
            };
            break;
          case "keyword-description-page":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVoList: answerReportVoList.filter((item) =>
                ["transferRelatedInformations"].includes(item.belong)
              ),
            };
            break;
          case "visit-duration-page":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVo: answerReportVoList.filter((item) =>
                ["visitDuration"].includes(item.belong)
              )[0],
            };
            break;
          case "visit-duration-page-table":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVo: answerReportVoList.filter((item) =>
                ["visitDuration"].includes(item.belong)
              )[0],
            };
            break;
          case "visit-date-page-table":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVo: answerReportVoList.filter((item) =>
                ["visitDate"].includes(item.belong)
              )[0],
            };
            break;
          case "visit-date-page":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVo: answerReportVoList.filter((item) =>
                ["visitDate"].includes(item.belong)
              )[0],
            };
            break;
          case "customer-feedback-page":
            item.pageObject = {
              parentUuid: item.type,
              answerReportVoList: answerReportVoList.filter((item) =>
                ["customerFeedback"].includes(item.belong)
              ),
            };
            break;
          case "research-conclusion-page":
            item.pageObject = {
              subTitle: "六、调研结论或建议",
              uuidKey: item.type,
              parentUuid: item.type,
              ...researchConclusionObject,
            };
            break;
          case "service-company-description":
            item.pageObject = {
              noTitle: true,
              borderOpen: true,
              uuidKey: item.type,
              parentUuid: item.type,
            };
            break;
          case "distribution-of-pharmacies":
            item.pageObject = {
              parentUuid: item.type,
              ...pharmaciesObject,
              ...commonObject,
            };
            break;
          case "execution-details-page":
            item.pageObject = {
              userReportVo,
              parentUuid: item.type,
              subTitle: "项目执行明细",
              planId: this.businessId,
              planType: ctype,
            };
            break;
          case "number-of-visits-to-pharmacies":
            item.pageObject = {
              subTitle: "项目执行人拜访药店数量统计",
              parentUuid: item.type,
              // drugPlanList
              answerReportVo: {
                formTemplateOptions: drugPlanList.map((item) => {
                  return {
                    optionValue: item.userName,
                    selectOptionNum: item.submitNum,
                    genderText: this.getEnumText(item.gender, genderResult),
                    userPhone: item.userPhone,
                    submitNum: item.submitNum,
                  };
                }),
              },
            };
            break;
        }
      });
      await this.$nextTick();
      this.thumbUpdateCount += 1;
      this.directoryUpdateCount += 1;
      this.backgroundUpdateCount += 1;
      this.projectExecutionProcessUpdateCount += 1;
      this.informationUpdateCount += 1;
      this.executionPersonnelDataUpdateCount += 1;
      this.dynamicsFormUpdateCount += 1;
      this.visitDateUpdateCount += 1;
      this.visitDurationUpdateCount += 1;
      this.keywordDescriptionUpdateCount += 1;
      this.descUpdateCount += 1;
      this.distributionOfUpdateCount += 1;
      this.executionDetailUpdateCount += 1;
      this.numberOfVisitUpdateCount += 1;
      this.visitDataPageUpdateCount += 1;
      this.updateSuccess();
    },
    async initMethod() {
      const saToken = getQueryStr("satoken");
      if (saToken) {
        this.saTime = 300;
      } else {
        // 客户端预览
        this.saTime = 2000;
      }
      this.exportProjectAccurateId();
    },
    async initEchart() {
      await loadScript(`${this.domainUrlV2}cdnjs/echarts.js`);
      await loadScript(`${this.domainUrlV2}cdnjs/echarts-gl.js`);
      this.initMethod();
    },
    pageInitMethod() {
      console.log("pageInitMethod========", this.initsuccesscount);
      return new Promise((resolve, reject) => {
        this.executionPersonnelDataRenderUpdateCount += 1;
        this.visitDurationRenderUpdateCount += 1;
        this.visitDateRenderUpdateCount += 1;
        this.keywordDescriptionRenderUpdateCount += 1;
        this.distributionOfRenderUpdateCount += 1;
        this.numberOfVisitRenderUpdateCount += 1;
        setTimeout(() => {
          resolve(true);
        }, 3000);
      });
    },
  },
  watch: {
    updatecount(n) {
      console.log("updatecount=======", n);
      this.pageLoading = true;
      this.initEchart();
    },
  },
  computed: {
    // 报告类型
    moduleReportType() {
      let pagetype = Number(this.pagetype);
      if (pagetype === 4) {
        // 拜访
        return 1;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.everyPage {
  overflow: hidden;
  background: #fff;
}
</style>
<style lang="scss">
@import "../css/earth-push-project-report.scss";
</style>