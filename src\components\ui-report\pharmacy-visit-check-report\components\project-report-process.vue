<template>
  <div class="project-report-process-page-v7">
    <template v-for="page in pageContent">
      <div
        class="project-report-process-page"
        :style="{
          height: pageSize.height + 'px',
          width: pageSize.width + 'px',
        }"
        :key="page.uuid"
      >
        <div class="project-report-process-i">
          <div class="project-report-process-i-t">
            <div class="project-report-process-i-tl"></div>
            三、项目验收流程
          </div>
          <div class="child-c">
            <div class="project-report-process-i-t2">
              <div class="project-report-process-i-t2-l"></div>
              项目执行概况
            </div>
            <div class="project-report-process-p b13">
              本项目由{{ pageObject.serviceProvider }}承接执行，执行周期为
              {{ pageObject.startTime2 }} 至
              {{ pageObject.endTime2 }}。执行期间，共有
              {{ pageObject.taskUserNum }} 名项目执行人员参与，累计完成 {{
                pageObject.taskNum
              }} 条任务。项目完成后， {{ pageObject.projectParty }}于{{
                pageObject.reportTimeText
              }}组织验收工作。
            </div>
            <div class="project-report-process-i-t2">
              <div class="project-report-process-i-t2-l"></div>
              验收流程
            </div>
            <div class="project-report-process-p">本次验收严格遵循流程</div>
          </div>
        </div>
        <div class="project-report-process-main">
          <template v-for="item in processResult">
            <div class="item-i" :key="item.uuid">
              <div class="project-report-process-main-i">
                <img
                  class="project-report-process-main-icon"
                  :src="item.icon"
                  alt=""
                />
                <div class="project-report-process-main-right">
                  <div class="project-report-process-main-right-t">
                    {{ item.title }}
                  </div>
                  <div class="proct-report-process-main-tip">
                    {{ item.content }}
                  </div>
                </div>
              </div>
              <div class="next-icon-item" v-if="item.showNext">
                <img class="next-icon" :src="nextIcon" />
              </div>
            </div>
          </template>
        </div>

        <pageBottomRectV2
          :uuid="page.uuid"
          :pageNumObject="pageNumObject"
        ></pageBottomRectV2>
      </div>
    </template>
  </div>
</template>

<script>
import toolMixin from "@/components/ui-report/mixins/tool.js";
export default {
  mixins: [toolMixin],
  inject: ["pageSize", "domainUrl", "pagetype", "filePrex"],
  props: {
    pageObject: {
      type: Object,
      default: function () {
        return {
          tenantName: "广东恒源数据服务有限公司",
          projectName: "广东绿葆网络发展有限公司",
        };
      },
    },
    updatecount: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      processResult: [
        {
          uuid: 10,
          title: "1.任务提交",
          content: "项目执行人拜访任务后整理成果提交",
          icon: this.domainUrl + this.filePrex + "icon-task-in.png?v=2",
          showNext: true,
        },
        {
          uuid: 20,
          title: "2.后台审核",
          content: "对任务真实性、完整性进行校验",
          icon: this.domainUrl + this.filePrex + "icon-task-audit.png?v=2",
          showNext: true,
        },
        {
          uuid: 30,
          title: "3.数据入库",
          content: "合格数据按标准格式归档",
          icon: this.domainUrl + this.filePrex + "icon-data-in.png?v=2",
          showNext: true,
        },
        {
          uuid: 40,
          title: "4.发起结算",
          content: "依据审核结果启动结算流程",
          icon: this.domainUrl + this.filePrex + "icon-task-account.png?v=2",
          showNext: true,
        },
        {
          uuid: 40,
          title: "5.项目验收",
          icon: this.domainUrl + this.filePrex + "icon-project-in.png?v=2",
          content: "经合规性审核后，最终形成验收结论",
        },
      ],
      taskInIcon: this.domainUrl + this.filePrex + "icon-task-in.png",
      taskAuditIcon: this.domainUrl + this.filePrex + "icon-task-audit.png",
      dataInIcon: this.domainUrl + this.filePrex + "icon-data-in.png",
      accountInIcon: this.domainUrl + this.filePrex + "icon-task-account.png",
      projectInIcon: this.domainUrl + this.filePrex + "icon-project-in.png",
      nextIcon: this.domainUrl + this.filePrex + "icon-next.png",
      subTitle: "",
      pageContent: [
        {
          type: "project-report-process-page",
          uuid: "project-report-process-page_0",
          children: [],
        },
      ],
    };
  },
  watch: {
    updatecount(n) {
      this.initMethod();
    },
  },
  methods: {
    async initMethod() {
      let pageObject = this.pageObject || {};
      let { subTitle } = pageObject;
      this.subTitle = subTitle;
      await this.$nextTick();
      this.trimSuccess();
    },
  },
};
</script>

<style lang="scss" scoped>
$mainColor: #e7effc;
$mainColorV2: #678ac8;
$mainColorV3: #6d8fca;
.project-report-process-page-v7 {
  background: #fff;
  font-family: Microsoft YaHei, Microsoft YaHei;

  .project-report-process-page {
    padding: 37px 20px 50px;
    box-sizing: border-box;
    position: relative;
  }
  .project-report-process-i {
    padding: 27px 27px 29px;
    background: $mainColor;
  }
  .project-report-process-i-t {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 32px;
    color: #678ac8;
  }
  .project-report-process-i-tl {
    width: 19px;
    height: 55px;
    background: $mainColorV2;
    border-radius: 23px;
    margin-right: 25px;
  }
  .project-report-process-i-t2 {
    display: flex;
    align-items: center;
    color: $mainColorV2;

    font-weight: bold;
    font-size: 24px;
    color: #678ac8;
    line-height: 36px;
    margin-bottom: 8px;
  }
  .project-report-process-i-t2-l {
    width: 8px;
    height: 19px;
    background: $mainColorV3;
    border-radius: 44px;
    margin-right: 13px;
  }
  .child-c {
    padding-left: 44px;
  }
  .b13 {
    margin-bottom: 13px;
  }
  .project-report-process-p {
    font-weight: 400;
    font-size: 18px;
    color: #333333;
    line-height: 28px;
  }
  .project-report-process-main-i {
    border: 1px solid $mainColorV3;
    border-radius: 16px;
    padding: 11px 55px;
    display: flex;
  }
  .project-report-process-main-icon {
    width: 67px;
    height: 67px;
    vertical-align: top;
    min-width: 67px;
    margin-right: 13px;
  }
  .project-report-process-main-right {
    flex: 1;
  }
  .project-report-process-main-right-t {
    font-weight: bold;
    font-size: 24px;
    color: #6d8fca;
    line-height: 31px;
  }
  .proct-report-process-main-tip {
    font-weight: 400;
    font-size: 16px;
    color: #6d8fca;
    line-height: 20px;
  }
  .project-report-process-main {
    padding-top: 21px;
    width: 345px;
    margin: 0 auto;
  }
  .next-icon {
    // height: 28px;
    margin-top: 9px;
    width: 22px;
    height: 33px;
    margin-bottom: 7px;
  }
  .next-icon-item {
    display: flex;
    justify-content: center;
  }
}
</style>