<template>
  <div
    class="page-bottom-rect-v2"
    :style="{
      '--background-img': 'url(' + iconUrl + ')',
    }"
  >
    <div class="page-bottom-rect-t"></div>
    <div class="page-bottom-rect-b">
      第
      {{ pageNumObject && pageNumObject[uuid] ? pageNumObject[uuid] : 1 }}
      页，共 {{ pageNumObject.totalNum || 1 }} 页
    </div>
  </div>
</template>

<script>
export default {
  inject: ["filePrex", "domainUrl"],
  props: {
    uuid: {
      type: String,
      default: null,
    },
    pageNumObject: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  data() {
    return {
      iconUrl: this.domainUrl + this.filePrex + "icon-bottom-rect.png",
    };
  },
};
</script>

<style lang="scss" scoped>
.page-bottom-rect-v2 {
  position: absolute;
  bottom: 20px;
  left: 32px;
  right: 32px;
  font-family: Microsoft YaHei UI, Microsoft YaHei UI;
  .page-bottom-rect-t {
    margin-bottom: 5px;
    width: 372px;
    height: 5px;
    // background: #70a5d7;
    background-image: var(--background-img);
    margin-left: auto;
  }
  .page-bottom-rect-b {
    font-weight: 400;
    font-size: 19px;
    color: #333333;
    text-align: right;
  }
}
</style>